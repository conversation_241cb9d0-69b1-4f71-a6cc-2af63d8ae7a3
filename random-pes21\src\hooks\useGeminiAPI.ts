import { useState } from 'react';
import { QuizQuestion, ChatMessage } from '../types/quiz';
import { QUIZ_CONSTANTS } from '../constants/quiz';

export function useGeminiAPI() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');

  const generateQuestions = async (category: string, language: string): Promise<QuizQuestion[]> => {
    if (!category.trim()) {
      throw new Error('Category cannot be empty.');
    }

    setLoading(true);
    setError('');

    const prompt = language === 'ar'
      ? `أنشئ 10 أسئلة اختبار عن كرة القدم حول ${category} بصيغة JSON. كل سؤال يجب أن يحتوي على "questionText" و مصفوفة "answerOptions". كل خيار إجابة يجب أن يحتوي على "answerText" و "isCorrect" boolean. تأكد من وجود إجابة صحيحة واحدة فقط لكل سؤال. اكتب الأسئلة والإجابات باللغة العربية باللهجة المصرية.`
      : `Generate 10 football trivia questions about ${category} in JSON format. Each question should have a "questionText" and an "answerOptions" array. Each answer option should have "answerText" and a "isCorrect" boolean. Ensure there is exactly one correct answer per question.`;

    const chatHistory: ChatMessage[] = [];
    chatHistory.push({ role: "user", parts: [{ text: prompt }] });
    
    const payload = {
      contents: chatHistory,
      generationConfig: {
        responseMimeType: "application/json",
        responseSchema: {
          type: "ARRAY",
          items: {
            type: "OBJECT",
            properties: {
              "questionText": { "type": "STRING" },
              "answerOptions": {
                "type": "ARRAY",
                "items": {
                  "type": "OBJECT",
                  "properties": {
                    "answerText": { "type": "STRING" },
                    "isCorrect": { "type": "BOOLEAN" }
                  },
                  "required": ["answerText", "isCorrect"]
                }
              }
            },
            "required": ["questionText", "answerOptions"]
          }
        }
      }
    };

    const apiUrl = `${QUIZ_CONSTANTS.API_URL}?key=${QUIZ_CONSTANTS.GEMINI_API_KEY}`;

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      
      if (result.candidates && result.candidates.length > 0 &&
          result.candidates[0].content && result.candidates[0].content.parts &&
          result.candidates[0].content.parts.length > 0) {
        const jsonText = result.candidates[0].content.parts[0].text;
        
        try {
          const parsedQuestions = JSON.parse(jsonText);
          if (Array.isArray(parsedQuestions) && parsedQuestions.length > 0) {
            return parsedQuestions;
          } else {
            throw new Error('Received invalid question format from API. Please try again.');
          }
        } catch {
          throw new Error('Failed to parse questions from API. Please try again.');
        }
      } else {
        throw new Error('Could not generate questions. Please try again.');
      }
    } catch (error) {
      const errorMessage = 'Failed to fetch questions. Network error or API issue.';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getExplanation = async (questionText: string, correctAnswer: string, language: string): Promise<string> => {
    setLoading(true);
    setError('');

    const prompt = language === 'ar'
      ? `اشرح الإجابة الصحيحة لسؤال كرة القدم التالي: "${questionText}". الإجابة الصحيحة هي "${correctAnswer}". قدم شرحاً مختصراً باللغة العربية باللهجة المصرية.`
      : `Explain the correct answer to the following football trivia question: "${questionText}". The correct answer is "${correctAnswer}". Provide a concise explanation.`;

    const chatHistory: ChatMessage[] = [];
    chatHistory.push({ role: "user", parts: [{ text: prompt }] });
    const payload = { contents: chatHistory };
    const apiUrl = `${QUIZ_CONSTANTS.API_URL}?key=${QUIZ_CONSTANTS.GEMINI_API_KEY}`;

    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      
      if (result.candidates && result.candidates.length > 0 &&
          result.candidates[0].content && result.candidates[0].content.parts &&
          result.candidates[0].content.parts.length > 0 && result.candidates[0].content.parts[0].text) {
        return result.candidates[0].content.parts[0].text;
      } else {
        throw new Error('Could not get an explanation. Please try again.');
      }
    } catch {
      const errorMessage = 'Failed to fetch explanation. Network error or API issue.';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    generateQuestions,
    getExplanation,
  };
}

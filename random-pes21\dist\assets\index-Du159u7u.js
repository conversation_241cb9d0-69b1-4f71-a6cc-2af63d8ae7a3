(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))r(a);new MutationObserver(a=>{for(const l of a)if(l.type==="childList")for(const o of l.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(a){const l={};return a.integrity&&(l.integrity=a.integrity),a.referrerPolicy&&(l.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?l.credentials="include":a.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(a){if(a.ep)return;a.ep=!0;const l=n(a);fetch(a.href,l)}})();var Gu={exports:{}},Aa={},Qu={exports:{}},j={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hr=Symbol.for("react.element"),Ed=Symbol.for("react.portal"),Pd=Symbol.for("react.fragment"),Fd=Symbol.for("react.strict_mode"),Ad=Symbol.for("react.profiler"),Rd=Symbol.for("react.provider"),Td=Symbol.for("react.context"),Md=Symbol.for("react.forward_ref"),zd=Symbol.for("react.suspense"),_d=Symbol.for("react.memo"),jd=Symbol.for("react.lazy"),Li=Symbol.iterator;function Bd(e){return e===null||typeof e!="object"?null:(e=Li&&e[Li]||e["@@iterator"],typeof e=="function"?e:null)}var bu={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Ju=Object.assign,Yu={};function Nn(e,t,n){this.props=e,this.context=t,this.refs=Yu,this.updater=n||bu}Nn.prototype.isReactComponent={};Nn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Nn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Xu(){}Xu.prototype=Nn.prototype;function So(e,t,n){this.props=e,this.context=t,this.refs=Yu,this.updater=n||bu}var wo=So.prototype=new Xu;wo.constructor=So;Ju(wo,Nn.prototype);wo.isPureReactComponent=!0;var Ei=Array.isArray,qu=Object.prototype.hasOwnProperty,xo={current:null},Zu={key:!0,ref:!0,__self:!0,__source:!0};function es(e,t,n){var r,a={},l=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(l=""+t.key),t)qu.call(t,r)&&!Zu.hasOwnProperty(r)&&(a[r]=t[r]);var i=arguments.length-2;if(i===1)a.children=n;else if(1<i){for(var u=Array(i),s=0;s<i;s++)u[s]=arguments[s+2];a.children=u}if(e&&e.defaultProps)for(r in i=e.defaultProps,i)a[r]===void 0&&(a[r]=i[r]);return{$$typeof:hr,type:e,key:l,ref:o,props:a,_owner:xo.current}}function Dd(e,t){return{$$typeof:hr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ko(e){return typeof e=="object"&&e!==null&&e.$$typeof===hr}function Id(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Pi=/\/+/g;function ba(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Id(""+e.key):t.toString(36)}function Wr(e,t,n,r,a){var l=typeof e;(l==="undefined"||l==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(l){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case hr:case Ed:o=!0}}if(o)return o=e,a=a(o),e=r===""?"."+ba(o,0):r,Ei(a)?(n="",e!=null&&(n=e.replace(Pi,"$&/")+"/"),Wr(a,t,n,"",function(s){return s})):a!=null&&(ko(a)&&(a=Dd(a,n+(!a.key||o&&o.key===a.key?"":(""+a.key).replace(Pi,"$&/")+"/")+e)),t.push(a)),1;if(o=0,r=r===""?".":r+":",Ei(e))for(var i=0;i<e.length;i++){l=e[i];var u=r+ba(l,i);o+=Wr(l,t,n,u,a)}else if(u=Bd(e),typeof u=="function")for(e=u.call(e),i=0;!(l=e.next()).done;)l=l.value,u=r+ba(l,i++),o+=Wr(l,t,n,u,a);else if(l==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function kr(e,t,n){if(e==null)return e;var r=[],a=0;return Wr(e,r,"","",function(l){return t.call(n,l,a++)}),r}function Od(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var he={current:null},Kr={transition:null},Ud={ReactCurrentDispatcher:he,ReactCurrentBatchConfig:Kr,ReactCurrentOwner:xo};function ts(){throw Error("act(...) is not supported in production builds of React.")}j.Children={map:kr,forEach:function(e,t,n){kr(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return kr(e,function(){t++}),t},toArray:function(e){return kr(e,function(t){return t})||[]},only:function(e){if(!ko(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};j.Component=Nn;j.Fragment=Pd;j.Profiler=Ad;j.PureComponent=So;j.StrictMode=Fd;j.Suspense=zd;j.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Ud;j.act=ts;j.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Ju({},e.props),a=e.key,l=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(l=t.ref,o=xo.current),t.key!==void 0&&(a=""+t.key),e.type&&e.type.defaultProps)var i=e.type.defaultProps;for(u in t)qu.call(t,u)&&!Zu.hasOwnProperty(u)&&(r[u]=t[u]===void 0&&i!==void 0?i[u]:t[u])}var u=arguments.length-2;if(u===1)r.children=n;else if(1<u){i=Array(u);for(var s=0;s<u;s++)i[s]=arguments[s+2];r.children=i}return{$$typeof:hr,type:e.type,key:a,ref:l,props:r,_owner:o}};j.createContext=function(e){return e={$$typeof:Td,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Rd,_context:e},e.Consumer=e};j.createElement=es;j.createFactory=function(e){var t=es.bind(null,e);return t.type=e,t};j.createRef=function(){return{current:null}};j.forwardRef=function(e){return{$$typeof:Md,render:e}};j.isValidElement=ko;j.lazy=function(e){return{$$typeof:jd,_payload:{_status:-1,_result:e},_init:Od}};j.memo=function(e,t){return{$$typeof:_d,type:e,compare:t===void 0?null:t}};j.startTransition=function(e){var t=Kr.transition;Kr.transition={};try{e()}finally{Kr.transition=t}};j.unstable_act=ts;j.useCallback=function(e,t){return he.current.useCallback(e,t)};j.useContext=function(e){return he.current.useContext(e)};j.useDebugValue=function(){};j.useDeferredValue=function(e){return he.current.useDeferredValue(e)};j.useEffect=function(e,t){return he.current.useEffect(e,t)};j.useId=function(){return he.current.useId()};j.useImperativeHandle=function(e,t,n){return he.current.useImperativeHandle(e,t,n)};j.useInsertionEffect=function(e,t){return he.current.useInsertionEffect(e,t)};j.useLayoutEffect=function(e,t){return he.current.useLayoutEffect(e,t)};j.useMemo=function(e,t){return he.current.useMemo(e,t)};j.useReducer=function(e,t,n){return he.current.useReducer(e,t,n)};j.useRef=function(e){return he.current.useRef(e)};j.useState=function(e){return he.current.useState(e)};j.useSyncExternalStore=function(e,t,n){return he.current.useSyncExternalStore(e,t,n)};j.useTransition=function(){return he.current.useTransition()};j.version="18.3.1";Qu.exports=j;var y=Qu.exports;/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $d=y,Vd=Symbol.for("react.element"),Hd=Symbol.for("react.fragment"),Wd=Object.prototype.hasOwnProperty,Kd=$d.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Gd={key:!0,ref:!0,__self:!0,__source:!0};function ns(e,t,n){var r,a={},l=null,o=null;n!==void 0&&(l=""+n),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)Wd.call(t,r)&&!Gd.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)a[r]===void 0&&(a[r]=t[r]);return{$$typeof:Vd,type:e,key:l,ref:o,props:a,_owner:Kd.current}}Aa.Fragment=Hd;Aa.jsx=ns;Aa.jsxs=ns;Gu.exports=Aa;var p=Gu.exports,rs={exports:{}},Le={},as={exports:{}},ls={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(F,M){var z=F.length;F.push(M);e:for(;0<z;){var H=z-1>>>1,J=F[H];if(0<a(J,M))F[H]=M,F[z]=J,z=H;else break e}}function n(F){return F.length===0?null:F[0]}function r(F){if(F.length===0)return null;var M=F[0],z=F.pop();if(z!==M){F[0]=z;e:for(var H=0,J=F.length,Kt=J>>>1;H<Kt;){var $e=2*(H+1)-1,Ln=F[$e],Je=$e+1,Gt=F[Je];if(0>a(Ln,z))Je<J&&0>a(Gt,Ln)?(F[H]=Gt,F[Je]=z,H=Je):(F[H]=Ln,F[$e]=z,H=$e);else if(Je<J&&0>a(Gt,z))F[H]=Gt,F[Je]=z,H=Je;else break e}}return M}function a(F,M){var z=F.sortIndex-M.sortIndex;return z!==0?z:F.id-M.id}if(typeof performance=="object"&&typeof performance.now=="function"){var l=performance;e.unstable_now=function(){return l.now()}}else{var o=Date,i=o.now();e.unstable_now=function(){return o.now()-i}}var u=[],s=[],m=1,f=null,h=3,v=!1,C=!1,N=!1,L=typeof setTimeout=="function"?setTimeout:null,d=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(F){for(var M=n(s);M!==null;){if(M.callback===null)r(s);else if(M.startTime<=F)r(s),M.sortIndex=M.expirationTime,t(u,M);else break;M=n(s)}}function S(F){if(N=!1,g(F),!C)if(n(u)!==null)C=!0,Wt(w);else{var M=n(s);M!==null&&_(S,M.startTime-F)}}function w(F,M){C=!1,N&&(N=!1,d(P),P=-1),v=!0;var z=h;try{for(g(M),f=n(u);f!==null&&(!(f.expirationTime>M)||F&&!q());){var H=f.callback;if(typeof H=="function"){f.callback=null,h=f.priorityLevel;var J=H(f.expirationTime<=M);M=e.unstable_now(),typeof J=="function"?f.callback=J:f===n(u)&&r(u),g(M)}else r(u);f=n(u)}if(f!==null)var Kt=!0;else{var $e=n(s);$e!==null&&_(S,$e.startTime-M),Kt=!1}return Kt}finally{f=null,h=z,v=!1}}var k=!1,E=null,P=-1,T=5,R=-1;function q(){return!(e.unstable_now()-R<T)}function Z(){if(E!==null){var F=e.unstable_now();R=F;var M=!0;try{M=E(!0,F)}finally{M?de():(k=!1,E=null)}}else k=!1}var de;if(typeof c=="function")de=function(){c(Z)};else if(typeof MessageChannel<"u"){var wr=new MessageChannel,xr=wr.port2;wr.port1.onmessage=Z,de=function(){xr.postMessage(null)}}else de=function(){L(Z,0)};function Wt(F){E=F,k||(k=!0,de())}function _(F,M){P=L(function(){F(e.unstable_now())},M)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(F){F.callback=null},e.unstable_continueExecution=function(){C||v||(C=!0,Wt(w))},e.unstable_forceFrameRate=function(F){0>F||125<F?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):T=0<F?Math.floor(1e3/F):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(u)},e.unstable_next=function(F){switch(h){case 1:case 2:case 3:var M=3;break;default:M=h}var z=h;h=M;try{return F()}finally{h=z}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(F,M){switch(F){case 1:case 2:case 3:case 4:case 5:break;default:F=3}var z=h;h=F;try{return M()}finally{h=z}},e.unstable_scheduleCallback=function(F,M,z){var H=e.unstable_now();switch(typeof z=="object"&&z!==null?(z=z.delay,z=typeof z=="number"&&0<z?H+z:H):z=H,F){case 1:var J=-1;break;case 2:J=250;break;case 5:J=**********;break;case 4:J=1e4;break;default:J=5e3}return J=z+J,F={id:m++,callback:M,priorityLevel:F,startTime:z,expirationTime:J,sortIndex:-1},z>H?(F.sortIndex=z,t(s,F),n(u)===null&&F===n(s)&&(N?(d(P),P=-1):N=!0,_(S,z-H))):(F.sortIndex=J,t(u,F),C||v||(C=!0,Wt(w))),F},e.unstable_shouldYield=q,e.unstable_wrapCallback=function(F){var M=h;return function(){var z=h;h=M;try{return F.apply(this,arguments)}finally{h=z}}}})(ls);as.exports=ls;var Qd=as.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var bd=y,ke=Qd;function x(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var os=new Set,Jn={};function $t(e,t){fn(e,t),fn(e+"Capture",t)}function fn(e,t){for(Jn[e]=t,e=0;e<t.length;e++)os.add(t[e])}var nt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ll=Object.prototype.hasOwnProperty,Jd=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Fi={},Ai={};function Yd(e){return Ll.call(Ai,e)?!0:Ll.call(Fi,e)?!1:Jd.test(e)?Ai[e]=!0:(Fi[e]=!0,!1)}function Xd(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function qd(e,t,n,r){if(t===null||typeof t>"u"||Xd(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ge(e,t,n,r,a,l,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=o}var oe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){oe[e]=new ge(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];oe[t]=new ge(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){oe[e]=new ge(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){oe[e]=new ge(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){oe[e]=new ge(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){oe[e]=new ge(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){oe[e]=new ge(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){oe[e]=new ge(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){oe[e]=new ge(e,5,!1,e.toLowerCase(),null,!1,!1)});var Lo=/[\-:]([a-z])/g;function Eo(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Lo,Eo);oe[t]=new ge(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Lo,Eo);oe[t]=new ge(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Lo,Eo);oe[t]=new ge(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){oe[e]=new ge(e,1,!1,e.toLowerCase(),null,!1,!1)});oe.xlinkHref=new ge("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){oe[e]=new ge(e,1,!1,e.toLowerCase(),null,!0,!0)});function Po(e,t,n,r){var a=oe.hasOwnProperty(t)?oe[t]:null;(a!==null?a.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(qd(t,n,a,r)&&(n=null),r||a===null?Yd(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=n===null?a.type===3?!1:"":n:(t=a.attributeName,r=a.attributeNamespace,n===null?e.removeAttribute(t):(a=a.type,n=a===3||a===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var it=bd.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Lr=Symbol.for("react.element"),bt=Symbol.for("react.portal"),Jt=Symbol.for("react.fragment"),Fo=Symbol.for("react.strict_mode"),El=Symbol.for("react.profiler"),is=Symbol.for("react.provider"),us=Symbol.for("react.context"),Ao=Symbol.for("react.forward_ref"),Pl=Symbol.for("react.suspense"),Fl=Symbol.for("react.suspense_list"),Ro=Symbol.for("react.memo"),ct=Symbol.for("react.lazy"),ss=Symbol.for("react.offscreen"),Ri=Symbol.iterator;function Pn(e){return e===null||typeof e!="object"?null:(e=Ri&&e[Ri]||e["@@iterator"],typeof e=="function"?e:null)}var Q=Object.assign,Ja;function Bn(e){if(Ja===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ja=t&&t[1]||""}return`
`+Ja+e}var Ya=!1;function Xa(e,t){if(!e||Ya)return"";Ya=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(s){var r=s}Reflect.construct(e,[],t)}else{try{t.call()}catch(s){r=s}e.call(t.prototype)}else{try{throw Error()}catch(s){r=s}e()}}catch(s){if(s&&r&&typeof s.stack=="string"){for(var a=s.stack.split(`
`),l=r.stack.split(`
`),o=a.length-1,i=l.length-1;1<=o&&0<=i&&a[o]!==l[i];)i--;for(;1<=o&&0<=i;o--,i--)if(a[o]!==l[i]){if(o!==1||i!==1)do if(o--,i--,0>i||a[o]!==l[i]){var u=`
`+a[o].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}while(1<=o&&0<=i);break}}}finally{Ya=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Bn(e):""}function Zd(e){switch(e.tag){case 5:return Bn(e.type);case 16:return Bn("Lazy");case 13:return Bn("Suspense");case 19:return Bn("SuspenseList");case 0:case 2:case 15:return e=Xa(e.type,!1),e;case 11:return e=Xa(e.type.render,!1),e;case 1:return e=Xa(e.type,!0),e;default:return""}}function Al(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Jt:return"Fragment";case bt:return"Portal";case El:return"Profiler";case Fo:return"StrictMode";case Pl:return"Suspense";case Fl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case us:return(e.displayName||"Context")+".Consumer";case is:return(e._context.displayName||"Context")+".Provider";case Ao:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ro:return t=e.displayName||null,t!==null?t:Al(e.type)||"Memo";case ct:t=e._payload,e=e._init;try{return Al(e(t))}catch{}}return null}function em(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Al(t);case 8:return t===Fo?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function kt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function cs(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function tm(e){var t=cs(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(o){r=""+o,l.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Er(e){e._valueTracker||(e._valueTracker=tm(e))}function ds(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=cs(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function aa(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Rl(e,t){var n=t.checked;return Q({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ti(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=kt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function ms(e,t){t=t.checked,t!=null&&Po(e,"checked",t,!1)}function Tl(e,t){ms(e,t);var n=kt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Ml(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ml(e,t.type,kt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Mi(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Ml(e,t,n){(t!=="number"||aa(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Dn=Array.isArray;function on(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+kt(n),t=null,a=0;a<e.length;a++){if(e[a].value===n){e[a].selected=!0,r&&(e[a].defaultSelected=!0);return}t!==null||e[a].disabled||(t=e[a])}t!==null&&(t.selected=!0)}}function zl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(x(91));return Q({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function zi(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(x(92));if(Dn(n)){if(1<n.length)throw Error(x(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:kt(n)}}function fs(e,t){var n=kt(t.value),r=kt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function _i(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function hs(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function _l(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?hs(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Pr,gs=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,a){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,a)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Pr=Pr||document.createElement("div"),Pr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Pr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Yn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Un={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},nm=["Webkit","ms","Moz","O"];Object.keys(Un).forEach(function(e){nm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Un[t]=Un[e]})});function ps(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Un.hasOwnProperty(e)&&Un[e]?(""+t).trim():t+"px"}function ys(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,a=ps(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}var rm=Q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function jl(e,t){if(t){if(rm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(x(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(x(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(x(61))}if(t.style!=null&&typeof t.style!="object")throw Error(x(62))}}function Bl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Dl=null;function To(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Il=null,un=null,sn=null;function ji(e){if(e=yr(e)){if(typeof Il!="function")throw Error(x(280));var t=e.stateNode;t&&(t=_a(t),Il(e.stateNode,e.type,t))}}function vs(e){un?sn?sn.push(e):sn=[e]:un=e}function Cs(){if(un){var e=un,t=sn;if(sn=un=null,ji(e),t)for(e=0;e<t.length;e++)ji(t[e])}}function Ns(e,t){return e(t)}function Ss(){}var qa=!1;function ws(e,t,n){if(qa)return e(t,n);qa=!0;try{return Ns(e,t,n)}finally{qa=!1,(un!==null||sn!==null)&&(Ss(),Cs())}}function Xn(e,t){var n=e.stateNode;if(n===null)return null;var r=_a(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(x(231,t,typeof n));return n}var Ol=!1;if(nt)try{var Fn={};Object.defineProperty(Fn,"passive",{get:function(){Ol=!0}}),window.addEventListener("test",Fn,Fn),window.removeEventListener("test",Fn,Fn)}catch{Ol=!1}function am(e,t,n,r,a,l,o,i,u){var s=Array.prototype.slice.call(arguments,3);try{t.apply(n,s)}catch(m){this.onError(m)}}var $n=!1,la=null,oa=!1,Ul=null,lm={onError:function(e){$n=!0,la=e}};function om(e,t,n,r,a,l,o,i,u){$n=!1,la=null,am.apply(lm,arguments)}function im(e,t,n,r,a,l,o,i,u){if(om.apply(this,arguments),$n){if($n){var s=la;$n=!1,la=null}else throw Error(x(198));oa||(oa=!0,Ul=s)}}function Vt(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function xs(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Bi(e){if(Vt(e)!==e)throw Error(x(188))}function um(e){var t=e.alternate;if(!t){if(t=Vt(e),t===null)throw Error(x(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(a===null)break;var l=a.alternate;if(l===null){if(r=a.return,r!==null){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return Bi(a),e;if(l===r)return Bi(a),t;l=l.sibling}throw Error(x(188))}if(n.return!==r.return)n=a,r=l;else{for(var o=!1,i=a.child;i;){if(i===n){o=!0,n=a,r=l;break}if(i===r){o=!0,r=a,n=l;break}i=i.sibling}if(!o){for(i=l.child;i;){if(i===n){o=!0,n=l,r=a;break}if(i===r){o=!0,r=l,n=a;break}i=i.sibling}if(!o)throw Error(x(189))}}if(n.alternate!==r)throw Error(x(190))}if(n.tag!==3)throw Error(x(188));return n.stateNode.current===n?e:t}function ks(e){return e=um(e),e!==null?Ls(e):null}function Ls(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Ls(e);if(t!==null)return t;e=e.sibling}return null}var Es=ke.unstable_scheduleCallback,Di=ke.unstable_cancelCallback,sm=ke.unstable_shouldYield,cm=ke.unstable_requestPaint,Y=ke.unstable_now,dm=ke.unstable_getCurrentPriorityLevel,Mo=ke.unstable_ImmediatePriority,Ps=ke.unstable_UserBlockingPriority,ia=ke.unstable_NormalPriority,mm=ke.unstable_LowPriority,Fs=ke.unstable_IdlePriority,Ra=null,Ke=null;function fm(e){if(Ke&&typeof Ke.onCommitFiberRoot=="function")try{Ke.onCommitFiberRoot(Ra,e,void 0,(e.current.flags&128)===128)}catch{}}var De=Math.clz32?Math.clz32:pm,hm=Math.log,gm=Math.LN2;function pm(e){return e>>>=0,e===0?32:31-(hm(e)/gm|0)|0}var Fr=64,Ar=4194304;function In(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ua(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,a=e.suspendedLanes,l=e.pingedLanes,o=n&268435455;if(o!==0){var i=o&~a;i!==0?r=In(i):(l&=o,l!==0&&(r=In(l)))}else o=n&~a,o!==0?r=In(o):l!==0&&(r=In(l));if(r===0)return 0;if(t!==0&&t!==r&&!(t&a)&&(a=r&-r,l=t&-t,a>=l||a===16&&(l&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-De(t),a=1<<n,r|=e[n],t&=~a;return r}function ym(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function vm(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=e.pendingLanes;0<l;){var o=31-De(l),i=1<<o,u=a[o];u===-1?(!(i&n)||i&r)&&(a[o]=ym(i,t)):u<=t&&(e.expiredLanes|=i),l&=~i}}function $l(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function As(){var e=Fr;return Fr<<=1,!(Fr&4194240)&&(Fr=64),e}function Za(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function gr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-De(t),e[t]=n}function Cm(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-De(n),l=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~l}}function zo(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-De(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var D=0;function Rs(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Ts,_o,Ms,zs,_s,Vl=!1,Rr=[],pt=null,yt=null,vt=null,qn=new Map,Zn=new Map,mt=[],Nm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ii(e,t){switch(e){case"focusin":case"focusout":pt=null;break;case"dragenter":case"dragleave":yt=null;break;case"mouseover":case"mouseout":vt=null;break;case"pointerover":case"pointerout":qn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Zn.delete(t.pointerId)}}function An(e,t,n,r,a,l){return e===null||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},t!==null&&(t=yr(t),t!==null&&_o(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,a!==null&&t.indexOf(a)===-1&&t.push(a),e)}function Sm(e,t,n,r,a){switch(t){case"focusin":return pt=An(pt,e,t,n,r,a),!0;case"dragenter":return yt=An(yt,e,t,n,r,a),!0;case"mouseover":return vt=An(vt,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return qn.set(l,An(qn.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,Zn.set(l,An(Zn.get(l)||null,e,t,n,r,a)),!0}return!1}function js(e){var t=Tt(e.target);if(t!==null){var n=Vt(t);if(n!==null){if(t=n.tag,t===13){if(t=xs(n),t!==null){e.blockedOn=t,_s(e.priority,function(){Ms(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Gr(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Hl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Dl=r,n.target.dispatchEvent(r),Dl=null}else return t=yr(n),t!==null&&_o(t),e.blockedOn=n,!1;t.shift()}return!0}function Oi(e,t,n){Gr(e)&&n.delete(t)}function wm(){Vl=!1,pt!==null&&Gr(pt)&&(pt=null),yt!==null&&Gr(yt)&&(yt=null),vt!==null&&Gr(vt)&&(vt=null),qn.forEach(Oi),Zn.forEach(Oi)}function Rn(e,t){e.blockedOn===t&&(e.blockedOn=null,Vl||(Vl=!0,ke.unstable_scheduleCallback(ke.unstable_NormalPriority,wm)))}function er(e){function t(a){return Rn(a,e)}if(0<Rr.length){Rn(Rr[0],e);for(var n=1;n<Rr.length;n++){var r=Rr[n];r.blockedOn===e&&(r.blockedOn=null)}}for(pt!==null&&Rn(pt,e),yt!==null&&Rn(yt,e),vt!==null&&Rn(vt,e),qn.forEach(t),Zn.forEach(t),n=0;n<mt.length;n++)r=mt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<mt.length&&(n=mt[0],n.blockedOn===null);)js(n),n.blockedOn===null&&mt.shift()}var cn=it.ReactCurrentBatchConfig,sa=!0;function xm(e,t,n,r){var a=D,l=cn.transition;cn.transition=null;try{D=1,jo(e,t,n,r)}finally{D=a,cn.transition=l}}function km(e,t,n,r){var a=D,l=cn.transition;cn.transition=null;try{D=4,jo(e,t,n,r)}finally{D=a,cn.transition=l}}function jo(e,t,n,r){if(sa){var a=Hl(e,t,n,r);if(a===null)sl(e,t,r,ca,n),Ii(e,r);else if(Sm(a,e,t,n,r))r.stopPropagation();else if(Ii(e,r),t&4&&-1<Nm.indexOf(e)){for(;a!==null;){var l=yr(a);if(l!==null&&Ts(l),l=Hl(e,t,n,r),l===null&&sl(e,t,r,ca,n),l===a)break;a=l}a!==null&&r.stopPropagation()}else sl(e,t,r,null,n)}}var ca=null;function Hl(e,t,n,r){if(ca=null,e=To(r),e=Tt(e),e!==null)if(t=Vt(e),t===null)e=null;else if(n=t.tag,n===13){if(e=xs(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ca=e,null}function Bs(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(dm()){case Mo:return 1;case Ps:return 4;case ia:case mm:return 16;case Fs:return 536870912;default:return 16}default:return 16}}var ht=null,Bo=null,Qr=null;function Ds(){if(Qr)return Qr;var e,t=Bo,n=t.length,r,a="value"in ht?ht.value:ht.textContent,l=a.length;for(e=0;e<n&&t[e]===a[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===a[l-r];r++);return Qr=a.slice(e,1<r?1-r:void 0)}function br(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Tr(){return!0}function Ui(){return!1}function Ee(e){function t(n,r,a,l,o){this._reactName=n,this._targetInst=a,this.type=r,this.nativeEvent=l,this.target=o,this.currentTarget=null;for(var i in e)e.hasOwnProperty(i)&&(n=e[i],this[i]=n?n(l):l[i]);return this.isDefaultPrevented=(l.defaultPrevented!=null?l.defaultPrevented:l.returnValue===!1)?Tr:Ui,this.isPropagationStopped=Ui,this}return Q(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Tr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Tr)},persist:function(){},isPersistent:Tr}),t}var Sn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Do=Ee(Sn),pr=Q({},Sn,{view:0,detail:0}),Lm=Ee(pr),el,tl,Tn,Ta=Q({},pr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Io,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Tn&&(Tn&&e.type==="mousemove"?(el=e.screenX-Tn.screenX,tl=e.screenY-Tn.screenY):tl=el=0,Tn=e),el)},movementY:function(e){return"movementY"in e?e.movementY:tl}}),$i=Ee(Ta),Em=Q({},Ta,{dataTransfer:0}),Pm=Ee(Em),Fm=Q({},pr,{relatedTarget:0}),nl=Ee(Fm),Am=Q({},Sn,{animationName:0,elapsedTime:0,pseudoElement:0}),Rm=Ee(Am),Tm=Q({},Sn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Mm=Ee(Tm),zm=Q({},Sn,{data:0}),Vi=Ee(zm),_m={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},jm={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Bm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Dm(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Bm[e])?!!t[e]:!1}function Io(){return Dm}var Im=Q({},pr,{key:function(e){if(e.key){var t=_m[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=br(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?jm[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Io,charCode:function(e){return e.type==="keypress"?br(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?br(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Om=Ee(Im),Um=Q({},Ta,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Hi=Ee(Um),$m=Q({},pr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Io}),Vm=Ee($m),Hm=Q({},Sn,{propertyName:0,elapsedTime:0,pseudoElement:0}),Wm=Ee(Hm),Km=Q({},Ta,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Gm=Ee(Km),Qm=[9,13,27,32],Oo=nt&&"CompositionEvent"in window,Vn=null;nt&&"documentMode"in document&&(Vn=document.documentMode);var bm=nt&&"TextEvent"in window&&!Vn,Is=nt&&(!Oo||Vn&&8<Vn&&11>=Vn),Wi=" ",Ki=!1;function Os(e,t){switch(e){case"keyup":return Qm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Us(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Yt=!1;function Jm(e,t){switch(e){case"compositionend":return Us(t);case"keypress":return t.which!==32?null:(Ki=!0,Wi);case"textInput":return e=t.data,e===Wi&&Ki?null:e;default:return null}}function Ym(e,t){if(Yt)return e==="compositionend"||!Oo&&Os(e,t)?(e=Ds(),Qr=Bo=ht=null,Yt=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Is&&t.locale!=="ko"?null:t.data;default:return null}}var Xm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Gi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Xm[e.type]:t==="textarea"}function $s(e,t,n,r){vs(r),t=da(t,"onChange"),0<t.length&&(n=new Do("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Hn=null,tr=null;function qm(e){qs(e,0)}function Ma(e){var t=Zt(e);if(ds(t))return e}function Zm(e,t){if(e==="change")return t}var Vs=!1;if(nt){var rl;if(nt){var al="oninput"in document;if(!al){var Qi=document.createElement("div");Qi.setAttribute("oninput","return;"),al=typeof Qi.oninput=="function"}rl=al}else rl=!1;Vs=rl&&(!document.documentMode||9<document.documentMode)}function bi(){Hn&&(Hn.detachEvent("onpropertychange",Hs),tr=Hn=null)}function Hs(e){if(e.propertyName==="value"&&Ma(tr)){var t=[];$s(t,tr,e,To(e)),ws(qm,t)}}function ef(e,t,n){e==="focusin"?(bi(),Hn=t,tr=n,Hn.attachEvent("onpropertychange",Hs)):e==="focusout"&&bi()}function tf(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ma(tr)}function nf(e,t){if(e==="click")return Ma(t)}function rf(e,t){if(e==="input"||e==="change")return Ma(t)}function af(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Oe=typeof Object.is=="function"?Object.is:af;function nr(e,t){if(Oe(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!Ll.call(t,a)||!Oe(e[a],t[a]))return!1}return!0}function Ji(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Yi(e,t){var n=Ji(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ji(n)}}function Ws(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ws(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Ks(){for(var e=window,t=aa();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=aa(e.document)}return t}function Uo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function lf(e){var t=Ks(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Ws(n.ownerDocument.documentElement,n)){if(r!==null&&Uo(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var a=n.textContent.length,l=Math.min(r.start,a);r=r.end===void 0?l:Math.min(r.end,a),!e.extend&&l>r&&(a=r,r=l,l=a),a=Yi(n,l);var o=Yi(n,r);a&&o&&(e.rangeCount!==1||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(a.node,a.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var of=nt&&"documentMode"in document&&11>=document.documentMode,Xt=null,Wl=null,Wn=null,Kl=!1;function Xi(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Kl||Xt==null||Xt!==aa(r)||(r=Xt,"selectionStart"in r&&Uo(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Wn&&nr(Wn,r)||(Wn=r,r=da(Wl,"onSelect"),0<r.length&&(t=new Do("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Xt)))}function Mr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var qt={animationend:Mr("Animation","AnimationEnd"),animationiteration:Mr("Animation","AnimationIteration"),animationstart:Mr("Animation","AnimationStart"),transitionend:Mr("Transition","TransitionEnd")},ll={},Gs={};nt&&(Gs=document.createElement("div").style,"AnimationEvent"in window||(delete qt.animationend.animation,delete qt.animationiteration.animation,delete qt.animationstart.animation),"TransitionEvent"in window||delete qt.transitionend.transition);function za(e){if(ll[e])return ll[e];if(!qt[e])return e;var t=qt[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Gs)return ll[e]=t[n];return e}var Qs=za("animationend"),bs=za("animationiteration"),Js=za("animationstart"),Ys=za("transitionend"),Xs=new Map,qi="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Et(e,t){Xs.set(e,t),$t(t,[e])}for(var ol=0;ol<qi.length;ol++){var il=qi[ol],uf=il.toLowerCase(),sf=il[0].toUpperCase()+il.slice(1);Et(uf,"on"+sf)}Et(Qs,"onAnimationEnd");Et(bs,"onAnimationIteration");Et(Js,"onAnimationStart");Et("dblclick","onDoubleClick");Et("focusin","onFocus");Et("focusout","onBlur");Et(Ys,"onTransitionEnd");fn("onMouseEnter",["mouseout","mouseover"]);fn("onMouseLeave",["mouseout","mouseover"]);fn("onPointerEnter",["pointerout","pointerover"]);fn("onPointerLeave",["pointerout","pointerover"]);$t("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));$t("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));$t("onBeforeInput",["compositionend","keypress","textInput","paste"]);$t("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));$t("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));$t("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var On="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),cf=new Set("cancel close invalid load scroll toggle".split(" ").concat(On));function Zi(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,im(r,t,void 0,e),e.currentTarget=null}function qs(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],u=i.instance,s=i.currentTarget;if(i=i.listener,u!==l&&a.isPropagationStopped())break e;Zi(a,i,s),l=u}else for(o=0;o<r.length;o++){if(i=r[o],u=i.instance,s=i.currentTarget,i=i.listener,u!==l&&a.isPropagationStopped())break e;Zi(a,i,s),l=u}}}if(oa)throw e=Ul,oa=!1,Ul=null,e}function U(e,t){var n=t[Yl];n===void 0&&(n=t[Yl]=new Set);var r=e+"__bubble";n.has(r)||(Zs(t,e,2,!1),n.add(r))}function ul(e,t,n){var r=0;t&&(r|=4),Zs(n,e,r,t)}var zr="_reactListening"+Math.random().toString(36).slice(2);function rr(e){if(!e[zr]){e[zr]=!0,os.forEach(function(n){n!=="selectionchange"&&(cf.has(n)||ul(n,!1,e),ul(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[zr]||(t[zr]=!0,ul("selectionchange",!1,t))}}function Zs(e,t,n,r){switch(Bs(t)){case 1:var a=xm;break;case 4:a=km;break;default:a=jo}n=a.bind(null,t,n,e),a=void 0,!Ol||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(a=!0),r?a!==void 0?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):a!==void 0?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function sl(e,t,n,r,a){var l=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var i=r.stateNode.containerInfo;if(i===a||i.nodeType===8&&i.parentNode===a)break;if(o===4)for(o=r.return;o!==null;){var u=o.tag;if((u===3||u===4)&&(u=o.stateNode.containerInfo,u===a||u.nodeType===8&&u.parentNode===a))return;o=o.return}for(;i!==null;){if(o=Tt(i),o===null)return;if(u=o.tag,u===5||u===6){r=l=o;continue e}i=i.parentNode}}r=r.return}ws(function(){var s=l,m=To(n),f=[];e:{var h=Xs.get(e);if(h!==void 0){var v=Do,C=e;switch(e){case"keypress":if(br(n)===0)break e;case"keydown":case"keyup":v=Om;break;case"focusin":C="focus",v=nl;break;case"focusout":C="blur",v=nl;break;case"beforeblur":case"afterblur":v=nl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":v=$i;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":v=Pm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":v=Vm;break;case Qs:case bs:case Js:v=Rm;break;case Ys:v=Wm;break;case"scroll":v=Lm;break;case"wheel":v=Gm;break;case"copy":case"cut":case"paste":v=Mm;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":v=Hi}var N=(t&4)!==0,L=!N&&e==="scroll",d=N?h!==null?h+"Capture":null:h;N=[];for(var c=s,g;c!==null;){g=c;var S=g.stateNode;if(g.tag===5&&S!==null&&(g=S,d!==null&&(S=Xn(c,d),S!=null&&N.push(ar(c,S,g)))),L)break;c=c.return}0<N.length&&(h=new v(h,C,null,n,m),f.push({event:h,listeners:N}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",v=e==="mouseout"||e==="pointerout",h&&n!==Dl&&(C=n.relatedTarget||n.fromElement)&&(Tt(C)||C[rt]))break e;if((v||h)&&(h=m.window===m?m:(h=m.ownerDocument)?h.defaultView||h.parentWindow:window,v?(C=n.relatedTarget||n.toElement,v=s,C=C?Tt(C):null,C!==null&&(L=Vt(C),C!==L||C.tag!==5&&C.tag!==6)&&(C=null)):(v=null,C=s),v!==C)){if(N=$i,S="onMouseLeave",d="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(N=Hi,S="onPointerLeave",d="onPointerEnter",c="pointer"),L=v==null?h:Zt(v),g=C==null?h:Zt(C),h=new N(S,c+"leave",v,n,m),h.target=L,h.relatedTarget=g,S=null,Tt(m)===s&&(N=new N(d,c+"enter",C,n,m),N.target=g,N.relatedTarget=L,S=N),L=S,v&&C)t:{for(N=v,d=C,c=0,g=N;g;g=Qt(g))c++;for(g=0,S=d;S;S=Qt(S))g++;for(;0<c-g;)N=Qt(N),c--;for(;0<g-c;)d=Qt(d),g--;for(;c--;){if(N===d||d!==null&&N===d.alternate)break t;N=Qt(N),d=Qt(d)}N=null}else N=null;v!==null&&eu(f,h,v,N,!1),C!==null&&L!==null&&eu(f,L,C,N,!0)}}e:{if(h=s?Zt(s):window,v=h.nodeName&&h.nodeName.toLowerCase(),v==="select"||v==="input"&&h.type==="file")var w=Zm;else if(Gi(h))if(Vs)w=rf;else{w=tf;var k=ef}else(v=h.nodeName)&&v.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(w=nf);if(w&&(w=w(e,s))){$s(f,w,n,m);break e}k&&k(e,h,s),e==="focusout"&&(k=h._wrapperState)&&k.controlled&&h.type==="number"&&Ml(h,"number",h.value)}switch(k=s?Zt(s):window,e){case"focusin":(Gi(k)||k.contentEditable==="true")&&(Xt=k,Wl=s,Wn=null);break;case"focusout":Wn=Wl=Xt=null;break;case"mousedown":Kl=!0;break;case"contextmenu":case"mouseup":case"dragend":Kl=!1,Xi(f,n,m);break;case"selectionchange":if(of)break;case"keydown":case"keyup":Xi(f,n,m)}var E;if(Oo)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Yt?Os(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(Is&&n.locale!=="ko"&&(Yt||P!=="onCompositionStart"?P==="onCompositionEnd"&&Yt&&(E=Ds()):(ht=m,Bo="value"in ht?ht.value:ht.textContent,Yt=!0)),k=da(s,P),0<k.length&&(P=new Vi(P,e,null,n,m),f.push({event:P,listeners:k}),E?P.data=E:(E=Us(n),E!==null&&(P.data=E)))),(E=bm?Jm(e,n):Ym(e,n))&&(s=da(s,"onBeforeInput"),0<s.length&&(m=new Vi("onBeforeInput","beforeinput",null,n,m),f.push({event:m,listeners:s}),m.data=E))}qs(f,t)})}function ar(e,t,n){return{instance:e,listener:t,currentTarget:n}}function da(e,t){for(var n=t+"Capture",r=[];e!==null;){var a=e,l=a.stateNode;a.tag===5&&l!==null&&(a=l,l=Xn(e,n),l!=null&&r.unshift(ar(e,l,a)),l=Xn(e,t),l!=null&&r.push(ar(e,l,a))),e=e.return}return r}function Qt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function eu(e,t,n,r,a){for(var l=t._reactName,o=[];n!==null&&n!==r;){var i=n,u=i.alternate,s=i.stateNode;if(u!==null&&u===r)break;i.tag===5&&s!==null&&(i=s,a?(u=Xn(n,l),u!=null&&o.unshift(ar(n,u,i))):a||(u=Xn(n,l),u!=null&&o.push(ar(n,u,i)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var df=/\r\n?/g,mf=/\u0000|\uFFFD/g;function tu(e){return(typeof e=="string"?e:""+e).replace(df,`
`).replace(mf,"")}function _r(e,t,n){if(t=tu(t),tu(e)!==t&&n)throw Error(x(425))}function ma(){}var Gl=null,Ql=null;function bl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Jl=typeof setTimeout=="function"?setTimeout:void 0,ff=typeof clearTimeout=="function"?clearTimeout:void 0,nu=typeof Promise=="function"?Promise:void 0,hf=typeof queueMicrotask=="function"?queueMicrotask:typeof nu<"u"?function(e){return nu.resolve(null).then(e).catch(gf)}:Jl;function gf(e){setTimeout(function(){throw e})}function cl(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&a.nodeType===8)if(n=a.data,n==="/$"){if(r===0){e.removeChild(a),er(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=a}while(n);er(t)}function Ct(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ru(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var wn=Math.random().toString(36).slice(2),We="__reactFiber$"+wn,lr="__reactProps$"+wn,rt="__reactContainer$"+wn,Yl="__reactEvents$"+wn,pf="__reactListeners$"+wn,yf="__reactHandles$"+wn;function Tt(e){var t=e[We];if(t)return t;for(var n=e.parentNode;n;){if(t=n[rt]||n[We]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ru(e);e!==null;){if(n=e[We])return n;e=ru(e)}return t}e=n,n=e.parentNode}return null}function yr(e){return e=e[We]||e[rt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Zt(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(x(33))}function _a(e){return e[lr]||null}var Xl=[],en=-1;function Pt(e){return{current:e}}function $(e){0>en||(e.current=Xl[en],Xl[en]=null,en--)}function O(e,t){en++,Xl[en]=e.current,e.current=t}var Lt={},ce=Pt(Lt),ve=Pt(!1),Bt=Lt;function hn(e,t){var n=e.type.contextTypes;if(!n)return Lt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a={},l;for(l in n)a[l]=t[l];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function Ce(e){return e=e.childContextTypes,e!=null}function fa(){$(ve),$(ce)}function au(e,t,n){if(ce.current!==Lt)throw Error(x(168));O(ce,t),O(ve,n)}function ec(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var a in r)if(!(a in t))throw Error(x(108,em(e)||"Unknown",a));return Q({},n,r)}function ha(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Lt,Bt=ce.current,O(ce,e),O(ve,ve.current),!0}function lu(e,t,n){var r=e.stateNode;if(!r)throw Error(x(169));n?(e=ec(e,t,Bt),r.__reactInternalMemoizedMergedChildContext=e,$(ve),$(ce),O(ce,e)):$(ve),O(ve,n)}var Xe=null,ja=!1,dl=!1;function tc(e){Xe===null?Xe=[e]:Xe.push(e)}function vf(e){ja=!0,tc(e)}function Ft(){if(!dl&&Xe!==null){dl=!0;var e=0,t=D;try{var n=Xe;for(D=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Xe=null,ja=!1}catch(a){throw Xe!==null&&(Xe=Xe.slice(e+1)),Es(Mo,Ft),a}finally{D=t,dl=!1}}return null}var tn=[],nn=0,ga=null,pa=0,Pe=[],Fe=0,Dt=null,qe=1,Ze="";function At(e,t){tn[nn++]=pa,tn[nn++]=ga,ga=e,pa=t}function nc(e,t,n){Pe[Fe++]=qe,Pe[Fe++]=Ze,Pe[Fe++]=Dt,Dt=e;var r=qe;e=Ze;var a=32-De(r)-1;r&=~(1<<a),n+=1;var l=32-De(t)+a;if(30<l){var o=a-a%5;l=(r&(1<<o)-1).toString(32),r>>=o,a-=o,qe=1<<32-De(t)+a|n<<a|r,Ze=l+e}else qe=1<<l|n<<a|r,Ze=e}function $o(e){e.return!==null&&(At(e,1),nc(e,1,0))}function Vo(e){for(;e===ga;)ga=tn[--nn],tn[nn]=null,pa=tn[--nn],tn[nn]=null;for(;e===Dt;)Dt=Pe[--Fe],Pe[Fe]=null,Ze=Pe[--Fe],Pe[Fe]=null,qe=Pe[--Fe],Pe[Fe]=null}var xe=null,we=null,V=!1,Be=null;function rc(e,t){var n=Ae(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ou(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,xe=e,we=Ct(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,xe=e,we=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Dt!==null?{id:qe,overflow:Ze}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ae(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,xe=e,we=null,!0):!1;default:return!1}}function ql(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Zl(e){if(V){var t=we;if(t){var n=t;if(!ou(e,t)){if(ql(e))throw Error(x(418));t=Ct(n.nextSibling);var r=xe;t&&ou(e,t)?rc(r,n):(e.flags=e.flags&-4097|2,V=!1,xe=e)}}else{if(ql(e))throw Error(x(418));e.flags=e.flags&-4097|2,V=!1,xe=e}}}function iu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;xe=e}function jr(e){if(e!==xe)return!1;if(!V)return iu(e),V=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!bl(e.type,e.memoizedProps)),t&&(t=we)){if(ql(e))throw ac(),Error(x(418));for(;t;)rc(e,t),t=Ct(t.nextSibling)}if(iu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(x(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){we=Ct(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}we=null}}else we=xe?Ct(e.stateNode.nextSibling):null;return!0}function ac(){for(var e=we;e;)e=Ct(e.nextSibling)}function gn(){we=xe=null,V=!1}function Ho(e){Be===null?Be=[e]:Be.push(e)}var Cf=it.ReactCurrentBatchConfig;function Mn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(x(309));var r=n.stateNode}if(!r)throw Error(x(147,e));var a=r,l=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===l?t.ref:(t=function(o){var i=a.refs;o===null?delete i[l]:i[l]=o},t._stringRef=l,t)}if(typeof e!="string")throw Error(x(284));if(!n._owner)throw Error(x(290,e))}return e}function Br(e,t){throw e=Object.prototype.toString.call(t),Error(x(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function uu(e){var t=e._init;return t(e._payload)}function lc(e){function t(d,c){if(e){var g=d.deletions;g===null?(d.deletions=[c],d.flags|=16):g.push(c)}}function n(d,c){if(!e)return null;for(;c!==null;)t(d,c),c=c.sibling;return null}function r(d,c){for(d=new Map;c!==null;)c.key!==null?d.set(c.key,c):d.set(c.index,c),c=c.sibling;return d}function a(d,c){return d=xt(d,c),d.index=0,d.sibling=null,d}function l(d,c,g){return d.index=g,e?(g=d.alternate,g!==null?(g=g.index,g<c?(d.flags|=2,c):g):(d.flags|=2,c)):(d.flags|=1048576,c)}function o(d){return e&&d.alternate===null&&(d.flags|=2),d}function i(d,c,g,S){return c===null||c.tag!==6?(c=vl(g,d.mode,S),c.return=d,c):(c=a(c,g),c.return=d,c)}function u(d,c,g,S){var w=g.type;return w===Jt?m(d,c,g.props.children,S,g.key):c!==null&&(c.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===ct&&uu(w)===c.type)?(S=a(c,g.props),S.ref=Mn(d,c,g),S.return=d,S):(S=ta(g.type,g.key,g.props,null,d.mode,S),S.ref=Mn(d,c,g),S.return=d,S)}function s(d,c,g,S){return c===null||c.tag!==4||c.stateNode.containerInfo!==g.containerInfo||c.stateNode.implementation!==g.implementation?(c=Cl(g,d.mode,S),c.return=d,c):(c=a(c,g.children||[]),c.return=d,c)}function m(d,c,g,S,w){return c===null||c.tag!==7?(c=jt(g,d.mode,S,w),c.return=d,c):(c=a(c,g),c.return=d,c)}function f(d,c,g){if(typeof c=="string"&&c!==""||typeof c=="number")return c=vl(""+c,d.mode,g),c.return=d,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case Lr:return g=ta(c.type,c.key,c.props,null,d.mode,g),g.ref=Mn(d,null,c),g.return=d,g;case bt:return c=Cl(c,d.mode,g),c.return=d,c;case ct:var S=c._init;return f(d,S(c._payload),g)}if(Dn(c)||Pn(c))return c=jt(c,d.mode,g,null),c.return=d,c;Br(d,c)}return null}function h(d,c,g,S){var w=c!==null?c.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return w!==null?null:i(d,c,""+g,S);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case Lr:return g.key===w?u(d,c,g,S):null;case bt:return g.key===w?s(d,c,g,S):null;case ct:return w=g._init,h(d,c,w(g._payload),S)}if(Dn(g)||Pn(g))return w!==null?null:m(d,c,g,S,null);Br(d,g)}return null}function v(d,c,g,S,w){if(typeof S=="string"&&S!==""||typeof S=="number")return d=d.get(g)||null,i(c,d,""+S,w);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case Lr:return d=d.get(S.key===null?g:S.key)||null,u(c,d,S,w);case bt:return d=d.get(S.key===null?g:S.key)||null,s(c,d,S,w);case ct:var k=S._init;return v(d,c,g,k(S._payload),w)}if(Dn(S)||Pn(S))return d=d.get(g)||null,m(c,d,S,w,null);Br(c,S)}return null}function C(d,c,g,S){for(var w=null,k=null,E=c,P=c=0,T=null;E!==null&&P<g.length;P++){E.index>P?(T=E,E=null):T=E.sibling;var R=h(d,E,g[P],S);if(R===null){E===null&&(E=T);break}e&&E&&R.alternate===null&&t(d,E),c=l(R,c,P),k===null?w=R:k.sibling=R,k=R,E=T}if(P===g.length)return n(d,E),V&&At(d,P),w;if(E===null){for(;P<g.length;P++)E=f(d,g[P],S),E!==null&&(c=l(E,c,P),k===null?w=E:k.sibling=E,k=E);return V&&At(d,P),w}for(E=r(d,E);P<g.length;P++)T=v(E,d,P,g[P],S),T!==null&&(e&&T.alternate!==null&&E.delete(T.key===null?P:T.key),c=l(T,c,P),k===null?w=T:k.sibling=T,k=T);return e&&E.forEach(function(q){return t(d,q)}),V&&At(d,P),w}function N(d,c,g,S){var w=Pn(g);if(typeof w!="function")throw Error(x(150));if(g=w.call(g),g==null)throw Error(x(151));for(var k=w=null,E=c,P=c=0,T=null,R=g.next();E!==null&&!R.done;P++,R=g.next()){E.index>P?(T=E,E=null):T=E.sibling;var q=h(d,E,R.value,S);if(q===null){E===null&&(E=T);break}e&&E&&q.alternate===null&&t(d,E),c=l(q,c,P),k===null?w=q:k.sibling=q,k=q,E=T}if(R.done)return n(d,E),V&&At(d,P),w;if(E===null){for(;!R.done;P++,R=g.next())R=f(d,R.value,S),R!==null&&(c=l(R,c,P),k===null?w=R:k.sibling=R,k=R);return V&&At(d,P),w}for(E=r(d,E);!R.done;P++,R=g.next())R=v(E,d,P,R.value,S),R!==null&&(e&&R.alternate!==null&&E.delete(R.key===null?P:R.key),c=l(R,c,P),k===null?w=R:k.sibling=R,k=R);return e&&E.forEach(function(Z){return t(d,Z)}),V&&At(d,P),w}function L(d,c,g,S){if(typeof g=="object"&&g!==null&&g.type===Jt&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case Lr:e:{for(var w=g.key,k=c;k!==null;){if(k.key===w){if(w=g.type,w===Jt){if(k.tag===7){n(d,k.sibling),c=a(k,g.props.children),c.return=d,d=c;break e}}else if(k.elementType===w||typeof w=="object"&&w!==null&&w.$$typeof===ct&&uu(w)===k.type){n(d,k.sibling),c=a(k,g.props),c.ref=Mn(d,k,g),c.return=d,d=c;break e}n(d,k);break}else t(d,k);k=k.sibling}g.type===Jt?(c=jt(g.props.children,d.mode,S,g.key),c.return=d,d=c):(S=ta(g.type,g.key,g.props,null,d.mode,S),S.ref=Mn(d,c,g),S.return=d,d=S)}return o(d);case bt:e:{for(k=g.key;c!==null;){if(c.key===k)if(c.tag===4&&c.stateNode.containerInfo===g.containerInfo&&c.stateNode.implementation===g.implementation){n(d,c.sibling),c=a(c,g.children||[]),c.return=d,d=c;break e}else{n(d,c);break}else t(d,c);c=c.sibling}c=Cl(g,d.mode,S),c.return=d,d=c}return o(d);case ct:return k=g._init,L(d,c,k(g._payload),S)}if(Dn(g))return C(d,c,g,S);if(Pn(g))return N(d,c,g,S);Br(d,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,c!==null&&c.tag===6?(n(d,c.sibling),c=a(c,g),c.return=d,d=c):(n(d,c),c=vl(g,d.mode,S),c.return=d,d=c),o(d)):n(d,c)}return L}var pn=lc(!0),oc=lc(!1),ya=Pt(null),va=null,rn=null,Wo=null;function Ko(){Wo=rn=va=null}function Go(e){var t=ya.current;$(ya),e._currentValue=t}function eo(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function dn(e,t){va=e,Wo=rn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(ye=!0),e.firstContext=null)}function Te(e){var t=e._currentValue;if(Wo!==e)if(e={context:e,memoizedValue:t,next:null},rn===null){if(va===null)throw Error(x(308));rn=e,va.dependencies={lanes:0,firstContext:e}}else rn=rn.next=e;return t}var Mt=null;function Qo(e){Mt===null?Mt=[e]:Mt.push(e)}function ic(e,t,n,r){var a=t.interleaved;return a===null?(n.next=n,Qo(t)):(n.next=a.next,a.next=n),t.interleaved=n,at(e,r)}function at(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var dt=!1;function bo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function uc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function et(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Nt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,B&2){var a=r.pending;return a===null?t.next=t:(t.next=a.next,a.next=t),r.pending=t,at(e,n)}return a=r.interleaved,a===null?(t.next=t,Qo(r)):(t.next=a.next,a.next=t),r.interleaved=t,at(e,n)}function Jr(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,zo(e,n)}}function su(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var a=null,l=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};l===null?a=l=o:l=l.next=o,n=n.next}while(n!==null);l===null?a=l=t:l=l.next=t}else a=l=t;n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ca(e,t,n,r){var a=e.updateQueue;dt=!1;var l=a.firstBaseUpdate,o=a.lastBaseUpdate,i=a.shared.pending;if(i!==null){a.shared.pending=null;var u=i,s=u.next;u.next=null,o===null?l=s:o.next=s,o=u;var m=e.alternate;m!==null&&(m=m.updateQueue,i=m.lastBaseUpdate,i!==o&&(i===null?m.firstBaseUpdate=s:i.next=s,m.lastBaseUpdate=u))}if(l!==null){var f=a.baseState;o=0,m=s=u=null,i=l;do{var h=i.lane,v=i.eventTime;if((r&h)===h){m!==null&&(m=m.next={eventTime:v,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var C=e,N=i;switch(h=t,v=n,N.tag){case 1:if(C=N.payload,typeof C=="function"){f=C.call(v,f,h);break e}f=C;break e;case 3:C.flags=C.flags&-65537|128;case 0:if(C=N.payload,h=typeof C=="function"?C.call(v,f,h):C,h==null)break e;f=Q({},f,h);break e;case 2:dt=!0}}i.callback!==null&&i.lane!==0&&(e.flags|=64,h=a.effects,h===null?a.effects=[i]:h.push(i))}else v={eventTime:v,lane:h,tag:i.tag,payload:i.payload,callback:i.callback,next:null},m===null?(s=m=v,u=f):m=m.next=v,o|=h;if(i=i.next,i===null){if(i=a.shared.pending,i===null)break;h=i,i=h.next,h.next=null,a.lastBaseUpdate=h,a.shared.pending=null}}while(!0);if(m===null&&(u=f),a.baseState=u,a.firstBaseUpdate=s,a.lastBaseUpdate=m,t=a.shared.interleaved,t!==null){a=t;do o|=a.lane,a=a.next;while(a!==t)}else l===null&&(a.shared.lanes=0);Ot|=o,e.lanes=o,e.memoizedState=f}}function cu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(a!==null){if(r.callback=null,r=n,typeof a!="function")throw Error(x(191,a));a.call(r)}}}var vr={},Ge=Pt(vr),or=Pt(vr),ir=Pt(vr);function zt(e){if(e===vr)throw Error(x(174));return e}function Jo(e,t){switch(O(ir,t),O(or,e),O(Ge,vr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:_l(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=_l(t,e)}$(Ge),O(Ge,t)}function yn(){$(Ge),$(or),$(ir)}function sc(e){zt(ir.current);var t=zt(Ge.current),n=_l(t,e.type);t!==n&&(O(or,e),O(Ge,n))}function Yo(e){or.current===e&&($(Ge),$(or))}var W=Pt(0);function Na(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ml=[];function Xo(){for(var e=0;e<ml.length;e++)ml[e]._workInProgressVersionPrimary=null;ml.length=0}var Yr=it.ReactCurrentDispatcher,fl=it.ReactCurrentBatchConfig,It=0,K=null,ee=null,ne=null,Sa=!1,Kn=!1,ur=0,Nf=0;function ie(){throw Error(x(321))}function qo(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Oe(e[n],t[n]))return!1;return!0}function Zo(e,t,n,r,a,l){if(It=l,K=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Yr.current=e===null||e.memoizedState===null?kf:Lf,e=n(r,a),Kn){l=0;do{if(Kn=!1,ur=0,25<=l)throw Error(x(301));l+=1,ne=ee=null,t.updateQueue=null,Yr.current=Ef,e=n(r,a)}while(Kn)}if(Yr.current=wa,t=ee!==null&&ee.next!==null,It=0,ne=ee=K=null,Sa=!1,t)throw Error(x(300));return e}function ei(){var e=ur!==0;return ur=0,e}function He(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ne===null?K.memoizedState=ne=e:ne=ne.next=e,ne}function Me(){if(ee===null){var e=K.alternate;e=e!==null?e.memoizedState:null}else e=ee.next;var t=ne===null?K.memoizedState:ne.next;if(t!==null)ne=t,ee=e;else{if(e===null)throw Error(x(310));ee=e,e={memoizedState:ee.memoizedState,baseState:ee.baseState,baseQueue:ee.baseQueue,queue:ee.queue,next:null},ne===null?K.memoizedState=ne=e:ne=ne.next=e}return ne}function sr(e,t){return typeof t=="function"?t(e):t}function hl(e){var t=Me(),n=t.queue;if(n===null)throw Error(x(311));n.lastRenderedReducer=e;var r=ee,a=r.baseQueue,l=n.pending;if(l!==null){if(a!==null){var o=a.next;a.next=l.next,l.next=o}r.baseQueue=a=l,n.pending=null}if(a!==null){l=a.next,r=r.baseState;var i=o=null,u=null,s=l;do{var m=s.lane;if((It&m)===m)u!==null&&(u=u.next={lane:0,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null}),r=s.hasEagerState?s.eagerState:e(r,s.action);else{var f={lane:m,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null};u===null?(i=u=f,o=r):u=u.next=f,K.lanes|=m,Ot|=m}s=s.next}while(s!==null&&s!==l);u===null?o=r:u.next=i,Oe(r,t.memoizedState)||(ye=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=u,n.lastRenderedState=r}if(e=n.interleaved,e!==null){a=e;do l=a.lane,K.lanes|=l,Ot|=l,a=a.next;while(a!==e)}else a===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function gl(e){var t=Me(),n=t.queue;if(n===null)throw Error(x(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(a!==null){n.pending=null;var o=a=a.next;do l=e(l,o.action),o=o.next;while(o!==a);Oe(l,t.memoizedState)||(ye=!0),t.memoizedState=l,t.baseQueue===null&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function cc(){}function dc(e,t){var n=K,r=Me(),a=t(),l=!Oe(r.memoizedState,a);if(l&&(r.memoizedState=a,ye=!0),r=r.queue,ti(hc.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||ne!==null&&ne.memoizedState.tag&1){if(n.flags|=2048,cr(9,fc.bind(null,n,r,a,t),void 0,null),re===null)throw Error(x(349));It&30||mc(n,t,a)}return a}function mc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function fc(e,t,n,r){t.value=n,t.getSnapshot=r,gc(t)&&pc(e)}function hc(e,t,n){return n(function(){gc(t)&&pc(e)})}function gc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Oe(e,n)}catch{return!0}}function pc(e){var t=at(e,1);t!==null&&Ie(t,e,1,-1)}function du(e){var t=He();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:sr,lastRenderedState:e},t.queue=e,e=e.dispatch=xf.bind(null,K,e),[t.memoizedState,e]}function cr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=K.updateQueue,t===null?(t={lastEffect:null,stores:null},K.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function yc(){return Me().memoizedState}function Xr(e,t,n,r){var a=He();K.flags|=e,a.memoizedState=cr(1|t,n,void 0,r===void 0?null:r)}function Ba(e,t,n,r){var a=Me();r=r===void 0?null:r;var l=void 0;if(ee!==null){var o=ee.memoizedState;if(l=o.destroy,r!==null&&qo(r,o.deps)){a.memoizedState=cr(t,n,l,r);return}}K.flags|=e,a.memoizedState=cr(1|t,n,l,r)}function mu(e,t){return Xr(8390656,8,e,t)}function ti(e,t){return Ba(2048,8,e,t)}function vc(e,t){return Ba(4,2,e,t)}function Cc(e,t){return Ba(4,4,e,t)}function Nc(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Sc(e,t,n){return n=n!=null?n.concat([e]):null,Ba(4,4,Nc.bind(null,t,e),n)}function ni(){}function wc(e,t){var n=Me();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&qo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function xc(e,t){var n=Me();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&qo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function kc(e,t,n){return It&21?(Oe(n,t)||(n=As(),K.lanes|=n,Ot|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ye=!0),e.memoizedState=n)}function Sf(e,t){var n=D;D=n!==0&&4>n?n:4,e(!0);var r=fl.transition;fl.transition={};try{e(!1),t()}finally{D=n,fl.transition=r}}function Lc(){return Me().memoizedState}function wf(e,t,n){var r=wt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ec(e))Pc(t,n);else if(n=ic(e,t,n,r),n!==null){var a=fe();Ie(n,e,r,a),Fc(n,t,r)}}function xf(e,t,n){var r=wt(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ec(e))Pc(t,a);else{var l=e.alternate;if(e.lanes===0&&(l===null||l.lanes===0)&&(l=t.lastRenderedReducer,l!==null))try{var o=t.lastRenderedState,i=l(o,n);if(a.hasEagerState=!0,a.eagerState=i,Oe(i,o)){var u=t.interleaved;u===null?(a.next=a,Qo(t)):(a.next=u.next,u.next=a),t.interleaved=a;return}}catch{}finally{}n=ic(e,t,a,r),n!==null&&(a=fe(),Ie(n,e,r,a),Fc(n,t,r))}}function Ec(e){var t=e.alternate;return e===K||t!==null&&t===K}function Pc(e,t){Kn=Sa=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Fc(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,zo(e,n)}}var wa={readContext:Te,useCallback:ie,useContext:ie,useEffect:ie,useImperativeHandle:ie,useInsertionEffect:ie,useLayoutEffect:ie,useMemo:ie,useReducer:ie,useRef:ie,useState:ie,useDebugValue:ie,useDeferredValue:ie,useTransition:ie,useMutableSource:ie,useSyncExternalStore:ie,useId:ie,unstable_isNewReconciler:!1},kf={readContext:Te,useCallback:function(e,t){return He().memoizedState=[e,t===void 0?null:t],e},useContext:Te,useEffect:mu,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Xr(4194308,4,Nc.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Xr(4194308,4,e,t)},useInsertionEffect:function(e,t){return Xr(4,2,e,t)},useMemo:function(e,t){var n=He();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=He();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=wf.bind(null,K,e),[r.memoizedState,e]},useRef:function(e){var t=He();return e={current:e},t.memoizedState=e},useState:du,useDebugValue:ni,useDeferredValue:function(e){return He().memoizedState=e},useTransition:function(){var e=du(!1),t=e[0];return e=Sf.bind(null,e[1]),He().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=K,a=He();if(V){if(n===void 0)throw Error(x(407));n=n()}else{if(n=t(),re===null)throw Error(x(349));It&30||mc(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,mu(hc.bind(null,r,l,e),[e]),r.flags|=2048,cr(9,fc.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=He(),t=re.identifierPrefix;if(V){var n=Ze,r=qe;n=(r&~(1<<32-De(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ur++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Nf++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Lf={readContext:Te,useCallback:wc,useContext:Te,useEffect:ti,useImperativeHandle:Sc,useInsertionEffect:vc,useLayoutEffect:Cc,useMemo:xc,useReducer:hl,useRef:yc,useState:function(){return hl(sr)},useDebugValue:ni,useDeferredValue:function(e){var t=Me();return kc(t,ee.memoizedState,e)},useTransition:function(){var e=hl(sr)[0],t=Me().memoizedState;return[e,t]},useMutableSource:cc,useSyncExternalStore:dc,useId:Lc,unstable_isNewReconciler:!1},Ef={readContext:Te,useCallback:wc,useContext:Te,useEffect:ti,useImperativeHandle:Sc,useInsertionEffect:vc,useLayoutEffect:Cc,useMemo:xc,useReducer:gl,useRef:yc,useState:function(){return gl(sr)},useDebugValue:ni,useDeferredValue:function(e){var t=Me();return ee===null?t.memoizedState=e:kc(t,ee.memoizedState,e)},useTransition:function(){var e=gl(sr)[0],t=Me().memoizedState;return[e,t]},useMutableSource:cc,useSyncExternalStore:dc,useId:Lc,unstable_isNewReconciler:!1};function _e(e,t){if(e&&e.defaultProps){t=Q({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function to(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Q({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Da={isMounted:function(e){return(e=e._reactInternals)?Vt(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=fe(),a=wt(e),l=et(r,a);l.payload=t,n!=null&&(l.callback=n),t=Nt(e,l,a),t!==null&&(Ie(t,e,a,r),Jr(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=fe(),a=wt(e),l=et(r,a);l.tag=1,l.payload=t,n!=null&&(l.callback=n),t=Nt(e,l,a),t!==null&&(Ie(t,e,a,r),Jr(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=fe(),r=wt(e),a=et(n,r);a.tag=2,t!=null&&(a.callback=t),t=Nt(e,a,r),t!==null&&(Ie(t,e,r,n),Jr(t,e,r))}};function fu(e,t,n,r,a,l,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,l,o):t.prototype&&t.prototype.isPureReactComponent?!nr(n,r)||!nr(a,l):!0}function Ac(e,t,n){var r=!1,a=Lt,l=t.contextType;return typeof l=="object"&&l!==null?l=Te(l):(a=Ce(t)?Bt:ce.current,r=t.contextTypes,l=(r=r!=null)?hn(e,a):Lt),t=new t(n,l),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Da,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=l),t}function hu(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Da.enqueueReplaceState(t,t.state,null)}function no(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},bo(e);var l=t.contextType;typeof l=="object"&&l!==null?a.context=Te(l):(l=Ce(t)?Bt:ce.current,a.context=hn(e,l)),a.state=e.memoizedState,l=t.getDerivedStateFromProps,typeof l=="function"&&(to(e,t,l,n),a.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof a.getSnapshotBeforeUpdate=="function"||typeof a.UNSAFE_componentWillMount!="function"&&typeof a.componentWillMount!="function"||(t=a.state,typeof a.componentWillMount=="function"&&a.componentWillMount(),typeof a.UNSAFE_componentWillMount=="function"&&a.UNSAFE_componentWillMount(),t!==a.state&&Da.enqueueReplaceState(a,a.state,null),Ca(e,n,a,r),a.state=e.memoizedState),typeof a.componentDidMount=="function"&&(e.flags|=4194308)}function vn(e,t){try{var n="",r=t;do n+=Zd(r),r=r.return;while(r);var a=n}catch(l){a=`
Error generating stack: `+l.message+`
`+l.stack}return{value:e,source:t,stack:a,digest:null}}function pl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function ro(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Pf=typeof WeakMap=="function"?WeakMap:Map;function Rc(e,t,n){n=et(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ka||(ka=!0,ho=r),ro(e,t)},n}function Tc(e,t,n){n=et(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){ro(e,t)}}var l=e.stateNode;return l!==null&&typeof l.componentDidCatch=="function"&&(n.callback=function(){ro(e,t),typeof r!="function"&&(St===null?St=new Set([this]):St.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function gu(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Pf;var a=new Set;r.set(t,a)}else a=r.get(t),a===void 0&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=$f.bind(null,e,t,n),t.then(e,e))}function pu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function yu(e,t,n,r,a){return e.mode&1?(e.flags|=65536,e.lanes=a,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=et(-1,1),t.tag=2,Nt(n,t,1))),n.lanes|=1),e)}var Ff=it.ReactCurrentOwner,ye=!1;function me(e,t,n,r){t.child=e===null?oc(t,null,n,r):pn(t,e.child,n,r)}function vu(e,t,n,r,a){n=n.render;var l=t.ref;return dn(t,a),r=Zo(e,t,n,r,l,a),n=ei(),e!==null&&!ye?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,lt(e,t,a)):(V&&n&&$o(t),t.flags|=1,me(e,t,r,a),t.child)}function Cu(e,t,n,r,a){if(e===null){var l=n.type;return typeof l=="function"&&!ci(l)&&l.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=l,Mc(e,t,l,r,a)):(e=ta(n.type,null,r,t,t.mode,a),e.ref=t.ref,e.return=t,t.child=e)}if(l=e.child,!(e.lanes&a)){var o=l.memoizedProps;if(n=n.compare,n=n!==null?n:nr,n(o,r)&&e.ref===t.ref)return lt(e,t,a)}return t.flags|=1,e=xt(l,r),e.ref=t.ref,e.return=t,t.child=e}function Mc(e,t,n,r,a){if(e!==null){var l=e.memoizedProps;if(nr(l,r)&&e.ref===t.ref)if(ye=!1,t.pendingProps=r=l,(e.lanes&a)!==0)e.flags&131072&&(ye=!0);else return t.lanes=e.lanes,lt(e,t,a)}return ao(e,t,n,r,a)}function zc(e,t,n){var r=t.pendingProps,a=r.children,l=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},O(ln,Se),Se|=n;else{if(!(n&1073741824))return e=l!==null?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,O(ln,Se),Se|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=l!==null?l.baseLanes:n,O(ln,Se),Se|=r}else l!==null?(r=l.baseLanes|n,t.memoizedState=null):r=n,O(ln,Se),Se|=r;return me(e,t,a,n),t.child}function _c(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ao(e,t,n,r,a){var l=Ce(n)?Bt:ce.current;return l=hn(t,l),dn(t,a),n=Zo(e,t,n,r,l,a),r=ei(),e!==null&&!ye?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,lt(e,t,a)):(V&&r&&$o(t),t.flags|=1,me(e,t,n,a),t.child)}function Nu(e,t,n,r,a){if(Ce(n)){var l=!0;ha(t)}else l=!1;if(dn(t,a),t.stateNode===null)qr(e,t),Ac(t,n,r),no(t,n,r,a),r=!0;else if(e===null){var o=t.stateNode,i=t.memoizedProps;o.props=i;var u=o.context,s=n.contextType;typeof s=="object"&&s!==null?s=Te(s):(s=Ce(n)?Bt:ce.current,s=hn(t,s));var m=n.getDerivedStateFromProps,f=typeof m=="function"||typeof o.getSnapshotBeforeUpdate=="function";f||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(i!==r||u!==s)&&hu(t,o,r,s),dt=!1;var h=t.memoizedState;o.state=h,Ca(t,r,o,a),u=t.memoizedState,i!==r||h!==u||ve.current||dt?(typeof m=="function"&&(to(t,n,m,r),u=t.memoizedState),(i=dt||fu(t,n,i,r,h,u,s))?(f||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=s,r=i):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,uc(e,t),i=t.memoizedProps,s=t.type===t.elementType?i:_e(t.type,i),o.props=s,f=t.pendingProps,h=o.context,u=n.contextType,typeof u=="object"&&u!==null?u=Te(u):(u=Ce(n)?Bt:ce.current,u=hn(t,u));var v=n.getDerivedStateFromProps;(m=typeof v=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(i!==f||h!==u)&&hu(t,o,r,u),dt=!1,h=t.memoizedState,o.state=h,Ca(t,r,o,a);var C=t.memoizedState;i!==f||h!==C||ve.current||dt?(typeof v=="function"&&(to(t,n,v,r),C=t.memoizedState),(s=dt||fu(t,n,s,r,h,C,u)||!1)?(m||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,C,u),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,C,u)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=C),o.props=r,o.state=C,o.context=u,r=s):(typeof o.componentDidUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return lo(e,t,n,r,l,a)}function lo(e,t,n,r,a,l){_c(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return a&&lu(t,n,!1),lt(e,t,l);r=t.stateNode,Ff.current=t;var i=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=pn(t,e.child,null,l),t.child=pn(t,null,i,l)):me(e,t,i,l),t.memoizedState=r.state,a&&lu(t,n,!0),t.child}function jc(e){var t=e.stateNode;t.pendingContext?au(e,t.pendingContext,t.pendingContext!==t.context):t.context&&au(e,t.context,!1),Jo(e,t.containerInfo)}function Su(e,t,n,r,a){return gn(),Ho(a),t.flags|=256,me(e,t,n,r),t.child}var oo={dehydrated:null,treeContext:null,retryLane:0};function io(e){return{baseLanes:e,cachePool:null,transitions:null}}function Bc(e,t,n){var r=t.pendingProps,a=W.current,l=!1,o=(t.flags&128)!==0,i;if((i=o)||(i=e!==null&&e.memoizedState===null?!1:(a&2)!==0),i?(l=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(a|=1),O(W,a&1),e===null)return Zl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,l?(r=t.mode,l=t.child,o={mode:"hidden",children:o},!(r&1)&&l!==null?(l.childLanes=0,l.pendingProps=o):l=Ua(o,r,0,null),e=jt(e,r,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=io(n),t.memoizedState=oo,e):ri(t,o));if(a=e.memoizedState,a!==null&&(i=a.dehydrated,i!==null))return Af(e,t,o,r,i,a,n);if(l){l=r.fallback,o=t.mode,a=e.child,i=a.sibling;var u={mode:"hidden",children:r.children};return!(o&1)&&t.child!==a?(r=t.child,r.childLanes=0,r.pendingProps=u,t.deletions=null):(r=xt(a,u),r.subtreeFlags=a.subtreeFlags&14680064),i!==null?l=xt(i,l):(l=jt(l,o,n,null),l.flags|=2),l.return=t,r.return=t,r.sibling=l,t.child=r,r=l,l=t.child,o=e.child.memoizedState,o=o===null?io(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},l.memoizedState=o,l.childLanes=e.childLanes&~n,t.memoizedState=oo,r}return l=e.child,e=l.sibling,r=xt(l,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ri(e,t){return t=Ua({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Dr(e,t,n,r){return r!==null&&Ho(r),pn(t,e.child,null,n),e=ri(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Af(e,t,n,r,a,l,o){if(n)return t.flags&256?(t.flags&=-257,r=pl(Error(x(422))),Dr(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(l=r.fallback,a=t.mode,r=Ua({mode:"visible",children:r.children},a,0,null),l=jt(l,a,o,null),l.flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,t.mode&1&&pn(t,e.child,null,o),t.child.memoizedState=io(o),t.memoizedState=oo,l);if(!(t.mode&1))return Dr(e,t,o,null);if(a.data==="$!"){if(r=a.nextSibling&&a.nextSibling.dataset,r)var i=r.dgst;return r=i,l=Error(x(419)),r=pl(l,r,void 0),Dr(e,t,o,r)}if(i=(o&e.childLanes)!==0,ye||i){if(r=re,r!==null){switch(o&-o){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}a=a&(r.suspendedLanes|o)?0:a,a!==0&&a!==l.retryLane&&(l.retryLane=a,at(e,a),Ie(r,e,a,-1))}return si(),r=pl(Error(x(421))),Dr(e,t,o,r)}return a.data==="$?"?(t.flags|=128,t.child=e.child,t=Vf.bind(null,e),a._reactRetry=t,null):(e=l.treeContext,we=Ct(a.nextSibling),xe=t,V=!0,Be=null,e!==null&&(Pe[Fe++]=qe,Pe[Fe++]=Ze,Pe[Fe++]=Dt,qe=e.id,Ze=e.overflow,Dt=t),t=ri(t,r.children),t.flags|=4096,t)}function wu(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),eo(e.return,t,n)}function yl(e,t,n,r,a){var l=e.memoizedState;l===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Dc(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(me(e,t,r.children,n),r=W.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&wu(e,n,t);else if(e.tag===19)wu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(O(W,r),!(t.mode&1))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;n!==null;)e=n.alternate,e!==null&&Na(e)===null&&(a=n),n=n.sibling;n=a,n===null?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),yl(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;a!==null;){if(e=a.alternate,e!==null&&Na(e)===null){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}yl(t,!0,n,null,l);break;case"together":yl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function qr(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function lt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Ot|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(x(153));if(t.child!==null){for(e=t.child,n=xt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=xt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Rf(e,t,n){switch(t.tag){case 3:jc(t),gn();break;case 5:sc(t);break;case 1:Ce(t.type)&&ha(t);break;case 4:Jo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;O(ya,r._currentValue),r._currentValue=a;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(O(W,W.current&1),t.flags|=128,null):n&t.child.childLanes?Bc(e,t,n):(O(W,W.current&1),e=lt(e,t,n),e!==null?e.sibling:null);O(W,W.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Dc(e,t,n);t.flags|=128}if(a=t.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),O(W,W.current),r)break;return null;case 22:case 23:return t.lanes=0,zc(e,t,n)}return lt(e,t,n)}var Ic,uo,Oc,Uc;Ic=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};uo=function(){};Oc=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,zt(Ge.current);var l=null;switch(n){case"input":a=Rl(e,a),r=Rl(e,r),l=[];break;case"select":a=Q({},a,{value:void 0}),r=Q({},r,{value:void 0}),l=[];break;case"textarea":a=zl(e,a),r=zl(e,r),l=[];break;default:typeof a.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ma)}jl(n,r);var o;n=null;for(s in a)if(!r.hasOwnProperty(s)&&a.hasOwnProperty(s)&&a[s]!=null)if(s==="style"){var i=a[s];for(o in i)i.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else s!=="dangerouslySetInnerHTML"&&s!=="children"&&s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Jn.hasOwnProperty(s)?l||(l=[]):(l=l||[]).push(s,null));for(s in r){var u=r[s];if(i=a!=null?a[s]:void 0,r.hasOwnProperty(s)&&u!==i&&(u!=null||i!=null))if(s==="style")if(i){for(o in i)!i.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&i[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(l||(l=[]),l.push(s,n)),n=u;else s==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,i=i?i.__html:void 0,u!=null&&i!==u&&(l=l||[]).push(s,u)):s==="children"?typeof u!="string"&&typeof u!="number"||(l=l||[]).push(s,""+u):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&(Jn.hasOwnProperty(s)?(u!=null&&s==="onScroll"&&U("scroll",e),l||i===u||(l=[])):(l=l||[]).push(s,u))}n&&(l=l||[]).push("style",n);var s=l;(t.updateQueue=s)&&(t.flags|=4)}};Uc=function(e,t,n,r){n!==r&&(t.flags|=4)};function zn(e,t){if(!V)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ue(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;a!==null;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags&14680064,r|=a.flags&14680064,a.return=e,a=a.sibling;else for(a=e.child;a!==null;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Tf(e,t,n){var r=t.pendingProps;switch(Vo(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ue(t),null;case 1:return Ce(t.type)&&fa(),ue(t),null;case 3:return r=t.stateNode,yn(),$(ve),$(ce),Xo(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(jr(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Be!==null&&(yo(Be),Be=null))),uo(e,t),ue(t),null;case 5:Yo(t);var a=zt(ir.current);if(n=t.type,e!==null&&t.stateNode!=null)Oc(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(x(166));return ue(t),null}if(e=zt(Ge.current),jr(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[We]=t,r[lr]=l,e=(t.mode&1)!==0,n){case"dialog":U("cancel",r),U("close",r);break;case"iframe":case"object":case"embed":U("load",r);break;case"video":case"audio":for(a=0;a<On.length;a++)U(On[a],r);break;case"source":U("error",r);break;case"img":case"image":case"link":U("error",r),U("load",r);break;case"details":U("toggle",r);break;case"input":Ti(r,l),U("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},U("invalid",r);break;case"textarea":zi(r,l),U("invalid",r)}jl(n,l),a=null;for(var o in l)if(l.hasOwnProperty(o)){var i=l[o];o==="children"?typeof i=="string"?r.textContent!==i&&(l.suppressHydrationWarning!==!0&&_r(r.textContent,i,e),a=["children",i]):typeof i=="number"&&r.textContent!==""+i&&(l.suppressHydrationWarning!==!0&&_r(r.textContent,i,e),a=["children",""+i]):Jn.hasOwnProperty(o)&&i!=null&&o==="onScroll"&&U("scroll",r)}switch(n){case"input":Er(r),Mi(r,l,!0);break;case"textarea":Er(r),_i(r);break;case"select":case"option":break;default:typeof l.onClick=="function"&&(r.onclick=ma)}r=a,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=a.nodeType===9?a:a.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=hs(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[We]=t,e[lr]=r,Ic(e,t,!1,!1),t.stateNode=e;e:{switch(o=Bl(n,r),n){case"dialog":U("cancel",e),U("close",e),a=r;break;case"iframe":case"object":case"embed":U("load",e),a=r;break;case"video":case"audio":for(a=0;a<On.length;a++)U(On[a],e);a=r;break;case"source":U("error",e),a=r;break;case"img":case"image":case"link":U("error",e),U("load",e),a=r;break;case"details":U("toggle",e),a=r;break;case"input":Ti(e,r),a=Rl(e,r),U("invalid",e);break;case"option":a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=Q({},r,{value:void 0}),U("invalid",e);break;case"textarea":zi(e,r),a=zl(e,r),U("invalid",e);break;default:a=r}jl(n,a),i=a;for(l in i)if(i.hasOwnProperty(l)){var u=i[l];l==="style"?ys(e,u):l==="dangerouslySetInnerHTML"?(u=u?u.__html:void 0,u!=null&&gs(e,u)):l==="children"?typeof u=="string"?(n!=="textarea"||u!=="")&&Yn(e,u):typeof u=="number"&&Yn(e,""+u):l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&l!=="autoFocus"&&(Jn.hasOwnProperty(l)?u!=null&&l==="onScroll"&&U("scroll",e):u!=null&&Po(e,l,u,o))}switch(n){case"input":Er(e),Mi(e,r,!1);break;case"textarea":Er(e),_i(e);break;case"option":r.value!=null&&e.setAttribute("value",""+kt(r.value));break;case"select":e.multiple=!!r.multiple,l=r.value,l!=null?on(e,!!r.multiple,l,!1):r.defaultValue!=null&&on(e,!!r.multiple,r.defaultValue,!0);break;default:typeof a.onClick=="function"&&(e.onclick=ma)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ue(t),null;case 6:if(e&&t.stateNode!=null)Uc(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(x(166));if(n=zt(ir.current),zt(Ge.current),jr(t)){if(r=t.stateNode,n=t.memoizedProps,r[We]=t,(l=r.nodeValue!==n)&&(e=xe,e!==null))switch(e.tag){case 3:_r(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&_r(r.nodeValue,n,(e.mode&1)!==0)}l&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[We]=t,t.stateNode=r}return ue(t),null;case 13:if($(W),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(V&&we!==null&&t.mode&1&&!(t.flags&128))ac(),gn(),t.flags|=98560,l=!1;else if(l=jr(t),r!==null&&r.dehydrated!==null){if(e===null){if(!l)throw Error(x(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(x(317));l[We]=t}else gn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ue(t),l=!1}else Be!==null&&(yo(Be),Be=null),l=!0;if(!l)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||W.current&1?te===0&&(te=3):si())),t.updateQueue!==null&&(t.flags|=4),ue(t),null);case 4:return yn(),uo(e,t),e===null&&rr(t.stateNode.containerInfo),ue(t),null;case 10:return Go(t.type._context),ue(t),null;case 17:return Ce(t.type)&&fa(),ue(t),null;case 19:if($(W),l=t.memoizedState,l===null)return ue(t),null;if(r=(t.flags&128)!==0,o=l.rendering,o===null)if(r)zn(l,!1);else{if(te!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Na(e),o!==null){for(t.flags|=128,zn(l,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)l=n,e=r,l.flags&=14680066,o=l.alternate,o===null?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=o.childLanes,l.lanes=o.lanes,l.child=o.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=o.memoizedProps,l.memoizedState=o.memoizedState,l.updateQueue=o.updateQueue,l.type=o.type,e=o.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return O(W,W.current&1|2),t.child}e=e.sibling}l.tail!==null&&Y()>Cn&&(t.flags|=128,r=!0,zn(l,!1),t.lanes=4194304)}else{if(!r)if(e=Na(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),zn(l,!0),l.tail===null&&l.tailMode==="hidden"&&!o.alternate&&!V)return ue(t),null}else 2*Y()-l.renderingStartTime>Cn&&n!==1073741824&&(t.flags|=128,r=!0,zn(l,!1),t.lanes=4194304);l.isBackwards?(o.sibling=t.child,t.child=o):(n=l.last,n!==null?n.sibling=o:t.child=o,l.last=o)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=Y(),t.sibling=null,n=W.current,O(W,r?n&1|2:n&1),t):(ue(t),null);case 22:case 23:return ui(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Se&1073741824&&(ue(t),t.subtreeFlags&6&&(t.flags|=8192)):ue(t),null;case 24:return null;case 25:return null}throw Error(x(156,t.tag))}function Mf(e,t){switch(Vo(t),t.tag){case 1:return Ce(t.type)&&fa(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return yn(),$(ve),$(ce),Xo(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Yo(t),null;case 13:if($(W),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(x(340));gn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return $(W),null;case 4:return yn(),null;case 10:return Go(t.type._context),null;case 22:case 23:return ui(),null;case 24:return null;default:return null}}var Ir=!1,se=!1,zf=typeof WeakSet=="function"?WeakSet:Set,A=null;function an(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){b(e,t,r)}else n.current=null}function so(e,t,n){try{n()}catch(r){b(e,t,r)}}var xu=!1;function _f(e,t){if(Gl=sa,e=Ks(),Uo(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch{n=null;break e}var o=0,i=-1,u=-1,s=0,m=0,f=e,h=null;t:for(;;){for(var v;f!==n||a!==0&&f.nodeType!==3||(i=o+a),f!==l||r!==0&&f.nodeType!==3||(u=o+r),f.nodeType===3&&(o+=f.nodeValue.length),(v=f.firstChild)!==null;)h=f,f=v;for(;;){if(f===e)break t;if(h===n&&++s===a&&(i=o),h===l&&++m===r&&(u=o),(v=f.nextSibling)!==null)break;f=h,h=f.parentNode}f=v}n=i===-1||u===-1?null:{start:i,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ql={focusedElem:e,selectionRange:n},sa=!1,A=t;A!==null;)if(t=A,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,A=e;else for(;A!==null;){t=A;try{var C=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(C!==null){var N=C.memoizedProps,L=C.memoizedState,d=t.stateNode,c=d.getSnapshotBeforeUpdate(t.elementType===t.type?N:_e(t.type,N),L);d.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(x(163))}}catch(S){b(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,A=e;break}A=t.return}return C=xu,xu=!1,C}function Gn(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var a=r=r.next;do{if((a.tag&e)===e){var l=a.destroy;a.destroy=void 0,l!==void 0&&so(t,n,l)}a=a.next}while(a!==r)}}function Ia(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function co(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function $c(e){var t=e.alternate;t!==null&&(e.alternate=null,$c(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[We],delete t[lr],delete t[Yl],delete t[pf],delete t[yf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Vc(e){return e.tag===5||e.tag===3||e.tag===4}function ku(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Vc(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function mo(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ma));else if(r!==4&&(e=e.child,e!==null))for(mo(e,t,n),e=e.sibling;e!==null;)mo(e,t,n),e=e.sibling}function fo(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(fo(e,t,n),e=e.sibling;e!==null;)fo(e,t,n),e=e.sibling}var ae=null,je=!1;function st(e,t,n){for(n=n.child;n!==null;)Hc(e,t,n),n=n.sibling}function Hc(e,t,n){if(Ke&&typeof Ke.onCommitFiberUnmount=="function")try{Ke.onCommitFiberUnmount(Ra,n)}catch{}switch(n.tag){case 5:se||an(n,t);case 6:var r=ae,a=je;ae=null,st(e,t,n),ae=r,je=a,ae!==null&&(je?(e=ae,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ae.removeChild(n.stateNode));break;case 18:ae!==null&&(je?(e=ae,n=n.stateNode,e.nodeType===8?cl(e.parentNode,n):e.nodeType===1&&cl(e,n),er(e)):cl(ae,n.stateNode));break;case 4:r=ae,a=je,ae=n.stateNode.containerInfo,je=!0,st(e,t,n),ae=r,je=a;break;case 0:case 11:case 14:case 15:if(!se&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){a=r=r.next;do{var l=a,o=l.destroy;l=l.tag,o!==void 0&&(l&2||l&4)&&so(n,t,o),a=a.next}while(a!==r)}st(e,t,n);break;case 1:if(!se&&(an(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){b(n,t,i)}st(e,t,n);break;case 21:st(e,t,n);break;case 22:n.mode&1?(se=(r=se)||n.memoizedState!==null,st(e,t,n),se=r):st(e,t,n);break;default:st(e,t,n)}}function Lu(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new zf),t.forEach(function(r){var a=Hf.bind(null,e,r);n.has(r)||(n.add(r),r.then(a,a))})}}function ze(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var a=n[r];try{var l=e,o=t,i=o;e:for(;i!==null;){switch(i.tag){case 5:ae=i.stateNode,je=!1;break e;case 3:ae=i.stateNode.containerInfo,je=!0;break e;case 4:ae=i.stateNode.containerInfo,je=!0;break e}i=i.return}if(ae===null)throw Error(x(160));Hc(l,o,a),ae=null,je=!1;var u=a.alternate;u!==null&&(u.return=null),a.return=null}catch(s){b(a,t,s)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Wc(t,e),t=t.sibling}function Wc(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ze(t,e),Ve(e),r&4){try{Gn(3,e,e.return),Ia(3,e)}catch(N){b(e,e.return,N)}try{Gn(5,e,e.return)}catch(N){b(e,e.return,N)}}break;case 1:ze(t,e),Ve(e),r&512&&n!==null&&an(n,n.return);break;case 5:if(ze(t,e),Ve(e),r&512&&n!==null&&an(n,n.return),e.flags&32){var a=e.stateNode;try{Yn(a,"")}catch(N){b(e,e.return,N)}}if(r&4&&(a=e.stateNode,a!=null)){var l=e.memoizedProps,o=n!==null?n.memoizedProps:l,i=e.type,u=e.updateQueue;if(e.updateQueue=null,u!==null)try{i==="input"&&l.type==="radio"&&l.name!=null&&ms(a,l),Bl(i,o);var s=Bl(i,l);for(o=0;o<u.length;o+=2){var m=u[o],f=u[o+1];m==="style"?ys(a,f):m==="dangerouslySetInnerHTML"?gs(a,f):m==="children"?Yn(a,f):Po(a,m,f,s)}switch(i){case"input":Tl(a,l);break;case"textarea":fs(a,l);break;case"select":var h=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!l.multiple;var v=l.value;v!=null?on(a,!!l.multiple,v,!1):h!==!!l.multiple&&(l.defaultValue!=null?on(a,!!l.multiple,l.defaultValue,!0):on(a,!!l.multiple,l.multiple?[]:"",!1))}a[lr]=l}catch(N){b(e,e.return,N)}}break;case 6:if(ze(t,e),Ve(e),r&4){if(e.stateNode===null)throw Error(x(162));a=e.stateNode,l=e.memoizedProps;try{a.nodeValue=l}catch(N){b(e,e.return,N)}}break;case 3:if(ze(t,e),Ve(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{er(t.containerInfo)}catch(N){b(e,e.return,N)}break;case 4:ze(t,e),Ve(e);break;case 13:ze(t,e),Ve(e),a=e.child,a.flags&8192&&(l=a.memoizedState!==null,a.stateNode.isHidden=l,!l||a.alternate!==null&&a.alternate.memoizedState!==null||(oi=Y())),r&4&&Lu(e);break;case 22:if(m=n!==null&&n.memoizedState!==null,e.mode&1?(se=(s=se)||m,ze(t,e),se=s):ze(t,e),Ve(e),r&8192){if(s=e.memoizedState!==null,(e.stateNode.isHidden=s)&&!m&&e.mode&1)for(A=e,m=e.child;m!==null;){for(f=A=m;A!==null;){switch(h=A,v=h.child,h.tag){case 0:case 11:case 14:case 15:Gn(4,h,h.return);break;case 1:an(h,h.return);var C=h.stateNode;if(typeof C.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,C.props=t.memoizedProps,C.state=t.memoizedState,C.componentWillUnmount()}catch(N){b(r,n,N)}}break;case 5:an(h,h.return);break;case 22:if(h.memoizedState!==null){Pu(f);continue}}v!==null?(v.return=h,A=v):Pu(f)}m=m.sibling}e:for(m=null,f=e;;){if(f.tag===5){if(m===null){m=f;try{a=f.stateNode,s?(l=a.style,typeof l.setProperty=="function"?l.setProperty("display","none","important"):l.display="none"):(i=f.stateNode,u=f.memoizedProps.style,o=u!=null&&u.hasOwnProperty("display")?u.display:null,i.style.display=ps("display",o))}catch(N){b(e,e.return,N)}}}else if(f.tag===6){if(m===null)try{f.stateNode.nodeValue=s?"":f.memoizedProps}catch(N){b(e,e.return,N)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;m===f&&(m=null),f=f.return}m===f&&(m=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ze(t,e),Ve(e),r&4&&Lu(e);break;case 21:break;default:ze(t,e),Ve(e)}}function Ve(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Vc(n)){var r=n;break e}n=n.return}throw Error(x(160))}switch(r.tag){case 5:var a=r.stateNode;r.flags&32&&(Yn(a,""),r.flags&=-33);var l=ku(e);fo(e,l,a);break;case 3:case 4:var o=r.stateNode.containerInfo,i=ku(e);mo(e,i,o);break;default:throw Error(x(161))}}catch(u){b(e,e.return,u)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function jf(e,t,n){A=e,Kc(e)}function Kc(e,t,n){for(var r=(e.mode&1)!==0;A!==null;){var a=A,l=a.child;if(a.tag===22&&r){var o=a.memoizedState!==null||Ir;if(!o){var i=a.alternate,u=i!==null&&i.memoizedState!==null||se;i=Ir;var s=se;if(Ir=o,(se=u)&&!s)for(A=a;A!==null;)o=A,u=o.child,o.tag===22&&o.memoizedState!==null?Fu(a):u!==null?(u.return=o,A=u):Fu(a);for(;l!==null;)A=l,Kc(l),l=l.sibling;A=a,Ir=i,se=s}Eu(e)}else a.subtreeFlags&8772&&l!==null?(l.return=a,A=l):Eu(e)}}function Eu(e){for(;A!==null;){var t=A;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:se||Ia(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!se)if(n===null)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:_e(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;l!==null&&cu(t,l,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}cu(t,o,n)}break;case 5:var i=t.stateNode;if(n===null&&t.flags&4){n=i;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var s=t.alternate;if(s!==null){var m=s.memoizedState;if(m!==null){var f=m.dehydrated;f!==null&&er(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(x(163))}se||t.flags&512&&co(t)}catch(h){b(t,t.return,h)}}if(t===e){A=null;break}if(n=t.sibling,n!==null){n.return=t.return,A=n;break}A=t.return}}function Pu(e){for(;A!==null;){var t=A;if(t===e){A=null;break}var n=t.sibling;if(n!==null){n.return=t.return,A=n;break}A=t.return}}function Fu(e){for(;A!==null;){var t=A;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ia(4,t)}catch(u){b(t,n,u)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var a=t.return;try{r.componentDidMount()}catch(u){b(t,a,u)}}var l=t.return;try{co(t)}catch(u){b(t,l,u)}break;case 5:var o=t.return;try{co(t)}catch(u){b(t,o,u)}}}catch(u){b(t,t.return,u)}if(t===e){A=null;break}var i=t.sibling;if(i!==null){i.return=t.return,A=i;break}A=t.return}}var Bf=Math.ceil,xa=it.ReactCurrentDispatcher,ai=it.ReactCurrentOwner,Re=it.ReactCurrentBatchConfig,B=0,re=null,X=null,le=0,Se=0,ln=Pt(0),te=0,dr=null,Ot=0,Oa=0,li=0,Qn=null,pe=null,oi=0,Cn=1/0,Ye=null,ka=!1,ho=null,St=null,Or=!1,gt=null,La=0,bn=0,go=null,Zr=-1,ea=0;function fe(){return B&6?Y():Zr!==-1?Zr:Zr=Y()}function wt(e){return e.mode&1?B&2&&le!==0?le&-le:Cf.transition!==null?(ea===0&&(ea=As()),ea):(e=D,e!==0||(e=window.event,e=e===void 0?16:Bs(e.type)),e):1}function Ie(e,t,n,r){if(50<bn)throw bn=0,go=null,Error(x(185));gr(e,n,r),(!(B&2)||e!==re)&&(e===re&&(!(B&2)&&(Oa|=n),te===4&&ft(e,le)),Ne(e,r),n===1&&B===0&&!(t.mode&1)&&(Cn=Y()+500,ja&&Ft()))}function Ne(e,t){var n=e.callbackNode;vm(e,t);var r=ua(e,e===re?le:0);if(r===0)n!==null&&Di(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Di(n),t===1)e.tag===0?vf(Au.bind(null,e)):tc(Au.bind(null,e)),hf(function(){!(B&6)&&Ft()}),n=null;else{switch(Rs(r)){case 1:n=Mo;break;case 4:n=Ps;break;case 16:n=ia;break;case 536870912:n=Fs;break;default:n=ia}n=Zc(n,Gc.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Gc(e,t){if(Zr=-1,ea=0,B&6)throw Error(x(327));var n=e.callbackNode;if(mn()&&e.callbackNode!==n)return null;var r=ua(e,e===re?le:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ea(e,r);else{t=r;var a=B;B|=2;var l=bc();(re!==e||le!==t)&&(Ye=null,Cn=Y()+500,_t(e,t));do try{Of();break}catch(i){Qc(e,i)}while(!0);Ko(),xa.current=l,B=a,X!==null?t=0:(re=null,le=0,t=te)}if(t!==0){if(t===2&&(a=$l(e),a!==0&&(r=a,t=po(e,a))),t===1)throw n=dr,_t(e,0),ft(e,r),Ne(e,Y()),n;if(t===6)ft(e,r);else{if(a=e.current.alternate,!(r&30)&&!Df(a)&&(t=Ea(e,r),t===2&&(l=$l(e),l!==0&&(r=l,t=po(e,l))),t===1))throw n=dr,_t(e,0),ft(e,r),Ne(e,Y()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(x(345));case 2:Rt(e,pe,Ye);break;case 3:if(ft(e,r),(r&130023424)===r&&(t=oi+500-Y(),10<t)){if(ua(e,0)!==0)break;if(a=e.suspendedLanes,(a&r)!==r){fe(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=Jl(Rt.bind(null,e,pe,Ye),t);break}Rt(e,pe,Ye);break;case 4:if(ft(e,r),(r&4194240)===r)break;for(t=e.eventTimes,a=-1;0<r;){var o=31-De(r);l=1<<o,o=t[o],o>a&&(a=o),r&=~l}if(r=a,r=Y()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Bf(r/1960))-r,10<r){e.timeoutHandle=Jl(Rt.bind(null,e,pe,Ye),r);break}Rt(e,pe,Ye);break;case 5:Rt(e,pe,Ye);break;default:throw Error(x(329))}}}return Ne(e,Y()),e.callbackNode===n?Gc.bind(null,e):null}function po(e,t){var n=Qn;return e.current.memoizedState.isDehydrated&&(_t(e,t).flags|=256),e=Ea(e,t),e!==2&&(t=pe,pe=n,t!==null&&yo(t)),e}function yo(e){pe===null?pe=e:pe.push.apply(pe,e)}function Df(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!Oe(l(),a))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ft(e,t){for(t&=~li,t&=~Oa,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-De(t),r=1<<n;e[n]=-1,t&=~r}}function Au(e){if(B&6)throw Error(x(327));mn();var t=ua(e,0);if(!(t&1))return Ne(e,Y()),null;var n=Ea(e,t);if(e.tag!==0&&n===2){var r=$l(e);r!==0&&(t=r,n=po(e,r))}if(n===1)throw n=dr,_t(e,0),ft(e,t),Ne(e,Y()),n;if(n===6)throw Error(x(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Rt(e,pe,Ye),Ne(e,Y()),null}function ii(e,t){var n=B;B|=1;try{return e(t)}finally{B=n,B===0&&(Cn=Y()+500,ja&&Ft())}}function Ut(e){gt!==null&&gt.tag===0&&!(B&6)&&mn();var t=B;B|=1;var n=Re.transition,r=D;try{if(Re.transition=null,D=1,e)return e()}finally{D=r,Re.transition=n,B=t,!(B&6)&&Ft()}}function ui(){Se=ln.current,$(ln)}function _t(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,ff(n)),X!==null)for(n=X.return;n!==null;){var r=n;switch(Vo(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&fa();break;case 3:yn(),$(ve),$(ce),Xo();break;case 5:Yo(r);break;case 4:yn();break;case 13:$(W);break;case 19:$(W);break;case 10:Go(r.type._context);break;case 22:case 23:ui()}n=n.return}if(re=e,X=e=xt(e.current,null),le=Se=t,te=0,dr=null,li=Oa=Ot=0,pe=Qn=null,Mt!==null){for(t=0;t<Mt.length;t++)if(n=Mt[t],r=n.interleaved,r!==null){n.interleaved=null;var a=r.next,l=n.pending;if(l!==null){var o=l.next;l.next=a,r.next=o}n.pending=r}Mt=null}return e}function Qc(e,t){do{var n=X;try{if(Ko(),Yr.current=wa,Sa){for(var r=K.memoizedState;r!==null;){var a=r.queue;a!==null&&(a.pending=null),r=r.next}Sa=!1}if(It=0,ne=ee=K=null,Kn=!1,ur=0,ai.current=null,n===null||n.return===null){te=1,dr=t,X=null;break}e:{var l=e,o=n.return,i=n,u=t;if(t=le,i.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){var s=u,m=i,f=m.tag;if(!(m.mode&1)&&(f===0||f===11||f===15)){var h=m.alternate;h?(m.updateQueue=h.updateQueue,m.memoizedState=h.memoizedState,m.lanes=h.lanes):(m.updateQueue=null,m.memoizedState=null)}var v=pu(o);if(v!==null){v.flags&=-257,yu(v,o,i,l,t),v.mode&1&&gu(l,s,t),t=v,u=s;var C=t.updateQueue;if(C===null){var N=new Set;N.add(u),t.updateQueue=N}else C.add(u);break e}else{if(!(t&1)){gu(l,s,t),si();break e}u=Error(x(426))}}else if(V&&i.mode&1){var L=pu(o);if(L!==null){!(L.flags&65536)&&(L.flags|=256),yu(L,o,i,l,t),Ho(vn(u,i));break e}}l=u=vn(u,i),te!==4&&(te=2),Qn===null?Qn=[l]:Qn.push(l),l=o;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t;var d=Rc(l,u,t);su(l,d);break e;case 1:i=u;var c=l.type,g=l.stateNode;if(!(l.flags&128)&&(typeof c.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(St===null||!St.has(g)))){l.flags|=65536,t&=-t,l.lanes|=t;var S=Tc(l,i,t);su(l,S);break e}}l=l.return}while(l!==null)}Yc(n)}catch(w){t=w,X===n&&n!==null&&(X=n=n.return);continue}break}while(!0)}function bc(){var e=xa.current;return xa.current=wa,e===null?wa:e}function si(){(te===0||te===3||te===2)&&(te=4),re===null||!(Ot&268435455)&&!(Oa&268435455)||ft(re,le)}function Ea(e,t){var n=B;B|=2;var r=bc();(re!==e||le!==t)&&(Ye=null,_t(e,t));do try{If();break}catch(a){Qc(e,a)}while(!0);if(Ko(),B=n,xa.current=r,X!==null)throw Error(x(261));return re=null,le=0,te}function If(){for(;X!==null;)Jc(X)}function Of(){for(;X!==null&&!sm();)Jc(X)}function Jc(e){var t=qc(e.alternate,e,Se);e.memoizedProps=e.pendingProps,t===null?Yc(e):X=t,ai.current=null}function Yc(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Mf(n,t),n!==null){n.flags&=32767,X=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{te=6,X=null;return}}else if(n=Tf(n,t,Se),n!==null){X=n;return}if(t=t.sibling,t!==null){X=t;return}X=t=e}while(t!==null);te===0&&(te=5)}function Rt(e,t,n){var r=D,a=Re.transition;try{Re.transition=null,D=1,Uf(e,t,n,r)}finally{Re.transition=a,D=r}return null}function Uf(e,t,n,r){do mn();while(gt!==null);if(B&6)throw Error(x(327));n=e.finishedWork;var a=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(x(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(Cm(e,l),e===re&&(X=re=null,le=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Or||(Or=!0,Zc(ia,function(){return mn(),null})),l=(n.flags&15990)!==0,n.subtreeFlags&15990||l){l=Re.transition,Re.transition=null;var o=D;D=1;var i=B;B|=4,ai.current=null,_f(e,n),Wc(n,e),lf(Ql),sa=!!Gl,Ql=Gl=null,e.current=n,jf(n),cm(),B=i,D=o,Re.transition=l}else e.current=n;if(Or&&(Or=!1,gt=e,La=a),l=e.pendingLanes,l===0&&(St=null),fm(n.stateNode),Ne(e,Y()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(ka)throw ka=!1,e=ho,ho=null,e;return La&1&&e.tag!==0&&mn(),l=e.pendingLanes,l&1?e===go?bn++:(bn=0,go=e):bn=0,Ft(),null}function mn(){if(gt!==null){var e=Rs(La),t=Re.transition,n=D;try{if(Re.transition=null,D=16>e?16:e,gt===null)var r=!1;else{if(e=gt,gt=null,La=0,B&6)throw Error(x(331));var a=B;for(B|=4,A=e.current;A!==null;){var l=A,o=l.child;if(A.flags&16){var i=l.deletions;if(i!==null){for(var u=0;u<i.length;u++){var s=i[u];for(A=s;A!==null;){var m=A;switch(m.tag){case 0:case 11:case 15:Gn(8,m,l)}var f=m.child;if(f!==null)f.return=m,A=f;else for(;A!==null;){m=A;var h=m.sibling,v=m.return;if($c(m),m===s){A=null;break}if(h!==null){h.return=v,A=h;break}A=v}}}var C=l.alternate;if(C!==null){var N=C.child;if(N!==null){C.child=null;do{var L=N.sibling;N.sibling=null,N=L}while(N!==null)}}A=l}}if(l.subtreeFlags&2064&&o!==null)o.return=l,A=o;else e:for(;A!==null;){if(l=A,l.flags&2048)switch(l.tag){case 0:case 11:case 15:Gn(9,l,l.return)}var d=l.sibling;if(d!==null){d.return=l.return,A=d;break e}A=l.return}}var c=e.current;for(A=c;A!==null;){o=A;var g=o.child;if(o.subtreeFlags&2064&&g!==null)g.return=o,A=g;else e:for(o=c;A!==null;){if(i=A,i.flags&2048)try{switch(i.tag){case 0:case 11:case 15:Ia(9,i)}}catch(w){b(i,i.return,w)}if(i===o){A=null;break e}var S=i.sibling;if(S!==null){S.return=i.return,A=S;break e}A=i.return}}if(B=a,Ft(),Ke&&typeof Ke.onPostCommitFiberRoot=="function")try{Ke.onPostCommitFiberRoot(Ra,e)}catch{}r=!0}return r}finally{D=n,Re.transition=t}}return!1}function Ru(e,t,n){t=vn(n,t),t=Rc(e,t,1),e=Nt(e,t,1),t=fe(),e!==null&&(gr(e,1,t),Ne(e,t))}function b(e,t,n){if(e.tag===3)Ru(e,e,n);else for(;t!==null;){if(t.tag===3){Ru(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(St===null||!St.has(r))){e=vn(n,e),e=Tc(t,e,1),t=Nt(t,e,1),e=fe(),t!==null&&(gr(t,1,e),Ne(t,e));break}}t=t.return}}function $f(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=fe(),e.pingedLanes|=e.suspendedLanes&n,re===e&&(le&n)===n&&(te===4||te===3&&(le&130023424)===le&&500>Y()-oi?_t(e,0):li|=n),Ne(e,t)}function Xc(e,t){t===0&&(e.mode&1?(t=Ar,Ar<<=1,!(Ar&130023424)&&(Ar=4194304)):t=1);var n=fe();e=at(e,t),e!==null&&(gr(e,t,n),Ne(e,n))}function Vf(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Xc(e,n)}function Hf(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;a!==null&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(x(314))}r!==null&&r.delete(t),Xc(e,n)}var qc;qc=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ve.current)ye=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return ye=!1,Rf(e,t,n);ye=!!(e.flags&131072)}else ye=!1,V&&t.flags&1048576&&nc(t,pa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;qr(e,t),e=t.pendingProps;var a=hn(t,ce.current);dn(t,n),a=Zo(null,t,r,e,a,n);var l=ei();return t.flags|=1,typeof a=="object"&&a!==null&&typeof a.render=="function"&&a.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ce(r)?(l=!0,ha(t)):l=!1,t.memoizedState=a.state!==null&&a.state!==void 0?a.state:null,bo(t),a.updater=Da,t.stateNode=a,a._reactInternals=t,no(t,r,e,n),t=lo(null,t,r,!0,l,n)):(t.tag=0,V&&l&&$o(t),me(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(qr(e,t),e=t.pendingProps,a=r._init,r=a(r._payload),t.type=r,a=t.tag=Kf(r),e=_e(r,e),a){case 0:t=ao(null,t,r,e,n);break e;case 1:t=Nu(null,t,r,e,n);break e;case 11:t=vu(null,t,r,e,n);break e;case 14:t=Cu(null,t,r,_e(r.type,e),n);break e}throw Error(x(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:_e(r,a),ao(e,t,r,a,n);case 1:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:_e(r,a),Nu(e,t,r,a,n);case 3:e:{if(jc(t),e===null)throw Error(x(387));r=t.pendingProps,l=t.memoizedState,a=l.element,uc(e,t),Ca(t,r,null,n);var o=t.memoizedState;if(r=o.element,l.isDehydrated)if(l={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=l,t.memoizedState=l,t.flags&256){a=vn(Error(x(423)),t),t=Su(e,t,r,n,a);break e}else if(r!==a){a=vn(Error(x(424)),t),t=Su(e,t,r,n,a);break e}else for(we=Ct(t.stateNode.containerInfo.firstChild),xe=t,V=!0,Be=null,n=oc(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(gn(),r===a){t=lt(e,t,n);break e}me(e,t,r,n)}t=t.child}return t;case 5:return sc(t),e===null&&Zl(t),r=t.type,a=t.pendingProps,l=e!==null?e.memoizedProps:null,o=a.children,bl(r,a)?o=null:l!==null&&bl(r,l)&&(t.flags|=32),_c(e,t),me(e,t,o,n),t.child;case 6:return e===null&&Zl(t),null;case 13:return Bc(e,t,n);case 4:return Jo(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=pn(t,null,r,n):me(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:_e(r,a),vu(e,t,r,a,n);case 7:return me(e,t,t.pendingProps,n),t.child;case 8:return me(e,t,t.pendingProps.children,n),t.child;case 12:return me(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,l=t.memoizedProps,o=a.value,O(ya,r._currentValue),r._currentValue=o,l!==null)if(Oe(l.value,o)){if(l.children===a.children&&!ve.current){t=lt(e,t,n);break e}}else for(l=t.child,l!==null&&(l.return=t);l!==null;){var i=l.dependencies;if(i!==null){o=l.child;for(var u=i.firstContext;u!==null;){if(u.context===r){if(l.tag===1){u=et(-1,n&-n),u.tag=2;var s=l.updateQueue;if(s!==null){s=s.shared;var m=s.pending;m===null?u.next=u:(u.next=m.next,m.next=u),s.pending=u}}l.lanes|=n,u=l.alternate,u!==null&&(u.lanes|=n),eo(l.return,n,t),i.lanes|=n;break}u=u.next}}else if(l.tag===10)o=l.type===t.type?null:l.child;else if(l.tag===18){if(o=l.return,o===null)throw Error(x(341));o.lanes|=n,i=o.alternate,i!==null&&(i.lanes|=n),eo(o,n,t),o=l.sibling}else o=l.child;if(o!==null)o.return=l;else for(o=l;o!==null;){if(o===t){o=null;break}if(l=o.sibling,l!==null){l.return=o.return,o=l;break}o=o.return}l=o}me(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,dn(t,n),a=Te(a),r=r(a),t.flags|=1,me(e,t,r,n),t.child;case 14:return r=t.type,a=_e(r,t.pendingProps),a=_e(r.type,a),Cu(e,t,r,a,n);case 15:return Mc(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:_e(r,a),qr(e,t),t.tag=1,Ce(r)?(e=!0,ha(t)):e=!1,dn(t,n),Ac(t,r,a),no(t,r,a,n),lo(null,t,r,!0,e,n);case 19:return Dc(e,t,n);case 22:return zc(e,t,n)}throw Error(x(156,t.tag))};function Zc(e,t){return Es(e,t)}function Wf(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ae(e,t,n,r){return new Wf(e,t,n,r)}function ci(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Kf(e){if(typeof e=="function")return ci(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ao)return 11;if(e===Ro)return 14}return 2}function xt(e,t){var n=e.alternate;return n===null?(n=Ae(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ta(e,t,n,r,a,l){var o=2;if(r=e,typeof e=="function")ci(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Jt:return jt(n.children,a,l,t);case Fo:o=8,a|=8;break;case El:return e=Ae(12,n,t,a|2),e.elementType=El,e.lanes=l,e;case Pl:return e=Ae(13,n,t,a),e.elementType=Pl,e.lanes=l,e;case Fl:return e=Ae(19,n,t,a),e.elementType=Fl,e.lanes=l,e;case ss:return Ua(n,a,l,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case is:o=10;break e;case us:o=9;break e;case Ao:o=11;break e;case Ro:o=14;break e;case ct:o=16,r=null;break e}throw Error(x(130,e==null?e:typeof e,""))}return t=Ae(o,n,t,a),t.elementType=e,t.type=r,t.lanes=l,t}function jt(e,t,n,r){return e=Ae(7,e,r,t),e.lanes=n,e}function Ua(e,t,n,r){return e=Ae(22,e,r,t),e.elementType=ss,e.lanes=n,e.stateNode={isHidden:!1},e}function vl(e,t,n){return e=Ae(6,e,null,t),e.lanes=n,e}function Cl(e,t,n){return t=Ae(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Gf(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Za(0),this.expirationTimes=Za(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Za(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function di(e,t,n,r,a,l,o,i,u){return e=new Gf(e,t,n,i,u),t===1?(t=1,l===!0&&(t|=8)):t=0,l=Ae(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},bo(l),e}function Qf(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:bt,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function ed(e){if(!e)return Lt;e=e._reactInternals;e:{if(Vt(e)!==e||e.tag!==1)throw Error(x(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ce(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(x(171))}if(e.tag===1){var n=e.type;if(Ce(n))return ec(e,n,t)}return t}function td(e,t,n,r,a,l,o,i,u){return e=di(n,r,!0,e,a,l,o,i,u),e.context=ed(null),n=e.current,r=fe(),a=wt(n),l=et(r,a),l.callback=t??null,Nt(n,l,a),e.current.lanes=a,gr(e,a,r),Ne(e,r),e}function $a(e,t,n,r){var a=t.current,l=fe(),o=wt(a);return n=ed(n),t.context===null?t.context=n:t.pendingContext=n,t=et(l,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Nt(a,t,o),e!==null&&(Ie(e,a,o,l),Jr(e,a,o)),o}function Pa(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Tu(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function mi(e,t){Tu(e,t),(e=e.alternate)&&Tu(e,t)}function bf(){return null}var nd=typeof reportError=="function"?reportError:function(e){console.error(e)};function fi(e){this._internalRoot=e}Va.prototype.render=fi.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(x(409));$a(e,t,null,null)};Va.prototype.unmount=fi.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Ut(function(){$a(null,e,null,null)}),t[rt]=null}};function Va(e){this._internalRoot=e}Va.prototype.unstable_scheduleHydration=function(e){if(e){var t=zs();e={blockedOn:null,target:e,priority:t};for(var n=0;n<mt.length&&t!==0&&t<mt[n].priority;n++);mt.splice(n,0,e),n===0&&js(e)}};function hi(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ha(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Mu(){}function Jf(e,t,n,r,a){if(a){if(typeof r=="function"){var l=r;r=function(){var s=Pa(o);l.call(s)}}var o=td(t,r,e,0,null,!1,!1,"",Mu);return e._reactRootContainer=o,e[rt]=o.current,rr(e.nodeType===8?e.parentNode:e),Ut(),o}for(;a=e.lastChild;)e.removeChild(a);if(typeof r=="function"){var i=r;r=function(){var s=Pa(u);i.call(s)}}var u=di(e,0,!1,null,null,!1,!1,"",Mu);return e._reactRootContainer=u,e[rt]=u.current,rr(e.nodeType===8?e.parentNode:e),Ut(function(){$a(t,u,n,r)}),u}function Wa(e,t,n,r,a){var l=n._reactRootContainer;if(l){var o=l;if(typeof a=="function"){var i=a;a=function(){var u=Pa(o);i.call(u)}}$a(t,o,e,a)}else o=Jf(n,t,e,a,r);return Pa(o)}Ts=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=In(t.pendingLanes);n!==0&&(zo(t,n|1),Ne(t,Y()),!(B&6)&&(Cn=Y()+500,Ft()))}break;case 13:Ut(function(){var r=at(e,1);if(r!==null){var a=fe();Ie(r,e,1,a)}}),mi(e,1)}};_o=function(e){if(e.tag===13){var t=at(e,134217728);if(t!==null){var n=fe();Ie(t,e,134217728,n)}mi(e,134217728)}};Ms=function(e){if(e.tag===13){var t=wt(e),n=at(e,t);if(n!==null){var r=fe();Ie(n,e,t,r)}mi(e,t)}};zs=function(){return D};_s=function(e,t){var n=D;try{return D=e,t()}finally{D=n}};Il=function(e,t,n){switch(t){case"input":if(Tl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=_a(r);if(!a)throw Error(x(90));ds(r),Tl(r,a)}}}break;case"textarea":fs(e,n);break;case"select":t=n.value,t!=null&&on(e,!!n.multiple,t,!1)}};Ns=ii;Ss=Ut;var Yf={usingClientEntryPoint:!1,Events:[yr,Zt,_a,vs,Cs,ii]},_n={findFiberByHostInstance:Tt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Xf={bundleType:_n.bundleType,version:_n.version,rendererPackageName:_n.rendererPackageName,rendererConfig:_n.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:it.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=ks(e),e===null?null:e.stateNode},findFiberByHostInstance:_n.findFiberByHostInstance||bf,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ur=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ur.isDisabled&&Ur.supportsFiber)try{Ra=Ur.inject(Xf),Ke=Ur}catch{}}Le.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Yf;Le.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!hi(t))throw Error(x(200));return Qf(e,t,null,n)};Le.createRoot=function(e,t){if(!hi(e))throw Error(x(299));var n=!1,r="",a=nd;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(a=t.onRecoverableError)),t=di(e,1,!1,null,null,n,!1,r,a),e[rt]=t.current,rr(e.nodeType===8?e.parentNode:e),new fi(t)};Le.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(x(188)):(e=Object.keys(e).join(","),Error(x(268,e)));return e=ks(t),e=e===null?null:e.stateNode,e};Le.flushSync=function(e){return Ut(e)};Le.hydrate=function(e,t,n){if(!Ha(t))throw Error(x(200));return Wa(null,e,t,!0,n)};Le.hydrateRoot=function(e,t,n){if(!hi(e))throw Error(x(405));var r=n!=null&&n.hydratedSources||null,a=!1,l="",o=nd;if(n!=null&&(n.unstable_strictMode===!0&&(a=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=td(t,null,e,1,n??null,a,!1,l,o),e[rt]=t.current,rr(e),r)for(e=0;e<r.length;e++)n=r[e],a=n._getVersion,a=a(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Va(t)};Le.render=function(e,t,n){if(!Ha(t))throw Error(x(200));return Wa(null,e,t,!1,n)};Le.unmountComponentAtNode=function(e){if(!Ha(e))throw Error(x(40));return e._reactRootContainer?(Ut(function(){Wa(null,null,e,!1,function(){e._reactRootContainer=null,e[rt]=null})}),!0):!1};Le.unstable_batchedUpdates=ii;Le.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Ha(n))throw Error(x(200));if(e==null||e._reactInternals===void 0)throw Error(x(38));return Wa(e,t,n,!1,r)};Le.version="18.3.1-next-f1338f8080-20240426";function rd(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(rd)}catch(e){console.error(e)}}rd(),rs.exports=Le;var qf=rs.exports,ad,zu=qf;ad=zu.createRoot,zu.hydrateRoot;const _u=({onClick:e,children:t,icon:n,variant:r="primary",title:a})=>{const l="py-3 px-6 text-lg font-bold rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 focus:outline-none flex items-center justify-center",o=r==="primary"?"gradient-yellow":"bg-gray-200 text-gray-800 hover:bg-gray-300";return p.jsxs("button",{onClick:e,className:`${l} ${o}`,style:{minWidth:"160px",cursor:"pointer"},title:a,children:[n&&p.jsx("span",{className:"mr-2",children:n}),t]})};/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zf=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),eh=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,n,r)=>r?r.toUpperCase():n.toLowerCase()),ju=e=>{const t=eh(e);return t.charAt(0).toUpperCase()+t.slice(1)},ld=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim(),th=e=>{for(const t in e)if(t.startsWith("aria-")||t==="role"||t==="title")return!0};/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var nh={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rh=y.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:a="",children:l,iconNode:o,...i},u)=>y.createElement("svg",{ref:u,...nh,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:ld("lucide",a),...!l&&!th(i)&&{"aria-hidden":"true"},...i},[...o.map(([s,m])=>y.createElement(s,m)),...Array.isArray(l)?l:[l]]));/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ue=(e,t)=>{const n=y.forwardRef(({className:r,...a},l)=>y.createElement(rh,{ref:l,iconNode:t,className:ld(`lucide-${Zf(ju(e))}`,`lucide-${e}`,r),...a}));return n.displayName=ju(e),n};/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ah=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]],od=Ue("image",ah);/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lh=[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]],oh=Ue("languages",lh);/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ih=[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]],uh=Ue("link",ih);/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sh=[["path",{d:"M7.21 15 2.66 7.14a2 2 0 0 1 .13-2.2L4.4 2.8A2 2 0 0 1 6 2h12a2 2 0 0 1 1.6.8l1.6 2.14a2 2 0 0 1 .14 2.2L16.79 15",key:"143lza"}],["path",{d:"M11 12 5.12 2.2",key:"qhuxz6"}],["path",{d:"m13 12 5.88-9.8",key:"hbye0f"}],["path",{d:"M8 7h8",key:"i86dvs"}],["circle",{cx:"12",cy:"17",r:"5",key:"qbz8iq"}],["path",{d:"M12 18v-2h-.5",key:"fawc4q"}]],ch=Ue("medal",sh);/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dh=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],mh=Ue("settings",dh);/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fh=[["path",{d:"M6 9H4.5a2.5 2.5 0 0 1 0-5H6",key:"17hqa7"}],["path",{d:"M18 9h1.5a2.5 2.5 0 0 0 0-5H18",key:"lmptdp"}],["path",{d:"M4 22h16",key:"57wxv0"}],["path",{d:"M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22",key:"1nw9bq"}],["path",{d:"M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22",key:"1np0yb"}],["path",{d:"M18 2H6v7a6 6 0 0 0 12 0V2Z",key:"u46fv3"}]],hh=Ue("trophy",fh);/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gh=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]],ph=Ue("upload",gh);/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yh=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],vh=Ue("user",yh);/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ch=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],Nh=Ue("users",Ch);/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sh=[["path",{d:"M11.1 7.1a16.55 16.55 0 0 1 10.9 4",key:"2880wi"}],["path",{d:"M12 12a12.6 12.6 0 0 1-8.7 5",key:"113sja"}],["path",{d:"M16.8 13.6a16.55 16.55 0 0 1-9 7.5",key:"1qmsgl"}],["path",{d:"M20.7 17a12.8 12.8 0 0 0-8.7-5 13.3 13.3 0 0 1 0-10",key:"1bmeqp"}],["path",{d:"M6.3 3.8a16.55 16.55 0 0 0 1.9 11.5",key:"iekzv9"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],wh=Ue("volleyball",Sh);/**
 * @license lucide-react v0.509.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xh=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],id=Ue("x",xh),kh=({gameData:e})=>{const{player1Club:t,player2Club:n,player1TopTeams:r,player2TopTeams:a,score:l,winner:o}=e,[i,u]=y.useState(null),s=f=>{u(i===f?null:f)},m=(f,h)=>p.jsxs("span",{className:"relative group",children:[p.jsx("span",{className:`text-yellow-400 ${f.league?"cursor-pointer underline-offset-4":""}`,onClick:()=>f.league&&s(h),children:f.name}),f.league&&p.jsxs(p.Fragment,{children:[p.jsx("span",{className:"hidden group-hover:block absolute z-10 bg-gray-800 text-white text-xs rounded py-1 px-2 -mt-1 left-1/2 transform -translate-x-1/2 whitespace-nowrap",children:f.league}),i===h&&p.jsx("span",{className:"md:hidden absolute z-10 bg-gray-800 text-white text-xs rounded py-1 px-2 -mt-1 left-1/2 transform -translate-x-1/2 whitespace-nowrap",children:f.league})]})]});return p.jsx("div",{className:"fade-in w-full max-w-2xl mx-auto mt-8 overflow-hidden rounded-lg shadow-lg",children:p.jsxs("table",{className:"w-full border-collapse",children:[p.jsx("thead",{children:p.jsxs("tr",{className:"gradient-red-blue",children:[p.jsxs("th",{className:"py-3 px-4 text-left",children:["Bobboo ~ ",m(t,"player1-club")]}),p.jsxs("th",{className:"py-3 px-4 text-left",children:["Kyrillos ~ ",m(n,"player2-club")]})]})}),p.jsxs("tbody",{children:[r.map((f,h)=>p.jsxs("tr",{className:h%2===0?"alternate-gradient-1":"alternate-gradient-2",children:[p.jsxs("td",{className:"py-3 px-4",children:[p.jsx("span",{className:"font-bold mr-0.5",children:h+1}),"-  ",f.name]}),p.jsx("td",{className:"py-3 px-4",children:a[h]?p.jsxs(p.Fragment,{children:[p.jsx("span",{className:"font-bold mr-0.5",children:h+1}),"-  ",a[h].name]}):p.jsx("span",{className:"text-gray-400",children:"No team available"})})]},h)),p.jsx("tr",{className:"gradient-yellow",children:p.jsxs("td",{colSpan:2,className:"py-3 px-4 font-bold",children:[p.jsxs("div",{className:"flex items-center",children:[p.jsx("span",{className:"text-black mr-2 relative top-3 font-bold text-lg",children:"Result:"}),p.jsxs("div",{className:"flex items-center flex-1 justify-center",children:[p.jsxs("span",{className:"text-black relative group",children:[p.jsx("span",{className:`${t.league?"cursor-pointer underline-offset-2":""}`,onClick:()=>t.league&&s("result-player1"),children:t.shortName||t.name}),t.league&&p.jsxs(p.Fragment,{children:[p.jsx("span",{className:"hidden group-hover:block absolute z-10 bg-gray-800 text-white text-xs rounded py-1 px-2 bottom-full mb-1 left-1/2 transform -translate-x-1/2 whitespace-nowrap",children:t.league}),i==="result-player1"&&p.jsx("span",{className:"md:hidden absolute z-10 bg-gray-800 text-white text-xs rounded py-1 px-2 bottom-full mb-1 left-1/2 transform -translate-x-1/2 whitespace-nowrap",children:t.league})]})]}),p.jsxs("span",{className:"text-black ml-2",children:[" ",l.player1]}),p.jsx("span",{className:"text-black mx-2",children:"–"}),p.jsxs("span",{className:"text-black mr-2",children:[l.player2," "]}),p.jsxs("span",{className:"text-black relative group",children:[p.jsx("span",{className:`${n.league?"cursor-pointer underline-offset-2":""}`,onClick:()=>n.league&&s("result-player2"),children:n.shortName||n.name}),n.league&&p.jsxs(p.Fragment,{children:[p.jsx("span",{className:"hidden group-hover:block absolute z-10 bg-gray-800 text-white text-xs rounded py-1 px-2 bottom-full mb-1 left-1/2 transform -translate-x-1/2 whitespace-nowrap",children:n.league}),i==="result-player2"&&p.jsx("span",{className:"md:hidden absolute z-10 bg-gray-800 text-white text-xs rounded py-1 px-2 bottom-full mb-1 left-1/2 transform -translate-x-1/2 whitespace-nowrap",children:n.league})]})]})]}),p.jsx("span",{className:"ml-2",children:p.jsx(hh,{size:25,className:"ml-2 mr-2 relative top-3"})})]}),p.jsxs("p",{className:"text-black text-sm text-center mt-1 flex justify-center",children:[p.jsx(ch,{size:17,className:"inline mr-1 ml-3"}),o===1?"Bobboo":"Kyrillos"," is the winner!"]})]})})]})]})})},Lh=[{name:"Camp Nou",url:"https://images.pexels.com/photos/13043589/pexels-photo-13043589.jpeg"},{name:"Night Stadium",url:"https://images.unsplash.com/photo-1489944440615-453fc2b6a9a9"},{name:"Training Ground",url:"https://images.pexels.com/photos/114296/pexels-photo-114296.jpeg"},{name:"Champions League",url:"https://img.freepik.com/free-photo/people-soccer-stadium_23-2151548540.jpg?t=st=1746923107~exp=1746926707~hmac=f2ce4978a39ae0ffc13cbbf65d5d4c8614d10f7a8053d0068382adb55b2ad3b5&w=1380"},{name:"Messi World Cup",url:"https://media.cnn.com/api/v1/images/stellar/prod/221218155450-29-world-cup-final-1218.jpg?q=w_3000,c_fill"},{name:"Lamine Yamal World",url:"https://www.fcbarcelona.com/photo-resources/2024/06/15/8139955f-15d9-4566-98b6-ccb0320ecf2d/Sin-t-tulo-1.jpg?width=2000&height=1550"},{name:"Pedri x Lamine x Flick",url:"https://www.fcbarcelona.com/photo-resources/2025/04/26/ce699303-9286-40ef-8af6-c54235800178/WEB-Copa-Final-MD2.png?width=2324&height=1940"},{name:"Dark",url:"https://st4.depositphotos.com/30440304/40080/i/450/depositphotos_400807534-stock-photo-abstract-background-colorful-wallpaper-intersecting.jpg"}],Nl="selectedBackground",Eh=({onSelect:e})=>{const t=y.useRef(null),[n,r]=y.useState(!1),[a,l]=y.useState("");y.useEffect(()=>{const m=localStorage.getItem(Nl);m&&e(JSON.parse(m))},[e]);const o=m=>{localStorage.setItem(Nl,JSON.stringify(m)),e(m)},i=()=>{localStorage.removeItem(Nl),e(null)},u=m=>{var h;const f=(h=m.target.files)==null?void 0:h[0];if(f){const v=new FileReader;v.onload=C=>{var L;const N={name:"Custom Background",url:(L=C.target)==null?void 0:L.result};o(N)},v.readAsDataURL(f)}},s=m=>{m.preventDefault(),a&&(o({name:"URL Background",url:a}),r(!1),l(""))};return p.jsxs("div",{className:"absolute top-4 left-4 z-10",children:[p.jsxs("div",{className:"dropdown",children:[p.jsx("button",{className:"gradient-yellow p-2 rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300",children:p.jsx(od,{size:24})}),p.jsxs("div",{className:"dropdown-content hidden group-hover:block absolute left-0 mt-2 w-64 bg-white rounded-lg shadow-xl",children:[p.jsxs("button",{className:"block w-full text-left px-4 py-2 hover:bg-gray-100 transition-colors duration-200 border-b border-gray-200",onClick:()=>{var m;return(m=t.current)==null?void 0:m.click()},children:[p.jsx(ph,{className:"inline-block mr-2",size:16}),"Upload Image"]}),p.jsxs("button",{className:"block w-full text-left px-4 py-2 hover:bg-gray-100 transition-colors duration-200 border-b border-gray-200",onClick:()=>r(!n),children:[p.jsx(uh,{className:"inline-block mr-2",size:16}),"Add Image URL"]}),n&&p.jsxs("form",{onSubmit:s,className:"p-2 border-b border-gray-200",children:[p.jsx("input",{type:"url",value:a,onChange:m=>l(m.target.value),placeholder:"Enter image URL",className:"w-full p-2 border rounded mb-2 text-sm",required:!0}),p.jsx("button",{type:"submit",className:"w-full px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors",children:"Set Background"})]}),p.jsxs("button",{className:"block w-full text-left px-4 py-2 hover:bg-gray-100 transition-colors duration-200 border-b border-gray-200 text-red-500",onClick:i,children:[p.jsx(id,{className:"inline-block mr-2",size:16}),"Remove Background"]}),p.jsx("div",{className:"py-1 px-4 text-xs text-gray-500 border-b border-gray-200",children:"Preset Backgrounds"}),Lh.map((m,f)=>p.jsx("button",{className:"block w-full text-left px-4 py-2 hover:bg-gray-100 transition-colors duration-200",onClick:()=>o(m),children:m.name},f))]})]}),p.jsx("input",{ref:t,type:"file",accept:"image/*",className:"hidden",onChange:u})]})},Ph=[{name:"Germany 🇩🇪",shortName:"GER 🇩🇪",strength:86},{name:"Spain 🇪🇸",shortName:"ESP 🇪🇸",strength:85},{name:"Argentina 🇦🇷",shortName:"ARG 🇦🇷",strength:87},{name:"Brazil 🇧🇷",shortName:"BRA 🇧🇷",strength:88},{name:"France 🇫🇷",shortName:"FRA 🇫🇷",strength:87},{name:"Portugal 🇵🇹",shortName:"POR 🇵🇹",strength:84},{name:"England 🏴󠁧󠁢󠁥󠁮󠁧󠁿",shortName:"ENG 🏴󠁧󠁢󠁥󠁮󠁧󠁿",strength:85},{name:"Netherlands 🇳🇱",shortName:"NED 🇳🇱",strength:83}],Fh=[{name:"FC Barcelona",shortName:"Barça",strength:88},{name:"Real Madrid",shortName:"Madrid",strength:87},{name:"Bayern Munich",shortName:"Bayern",strength:86},{name:"Manchester United",shortName:"Man Utd",strength:83},{name:"Liverpool",shortName:"Liverpool",strength:85},{name:"Chelsea",shortName:"Chelsea",strength:82},{name:"Manchester City",shortName:"Man City",strength:88},{name:"Paris Saint-Germain",shortName:"PSG",strength:85},{name:"Juventus",shortName:"Juventus",strength:81},{name:"AC Milan",shortName:"Milan",strength:80},{name:"Inter Milan",shortName:"Inter",strength:82},{name:"Arsenal",shortName:"Arsenal",strength:84},{name:"Borussia Dortmund",shortName:"Dortmund",strength:81},{name:"Atletico Madrid",shortName:"Atletico",strength:83},{name:"Tottenham Hotspur",shortName:"Spurs",strength:82},{name:"Ajax",shortName:"Ajax",strength:79},{name:"Napoli",shortName:"Napoli",strength:82},{name:"Benfica",shortName:"Benfica",strength:78},{name:"Porto",shortName:"Porto",strength:77},{name:"AS Roma",shortName:"Roma",strength:79}],ud=["Real Madrid","Paris Saint-Germain","Bayern Munich","Tottenham Hotspur","Juventus","Ajax","Napoli","AS Roma","Benfica","Borussia Dortmund","Atletico Madrid","Porto","AC Milan","Inter Milan","Chelsea","Manchester United"],Ah=e=>{let t=[...Fh];return e!=null&&e.excludedTeams&&(t=t.filter(n=>!e.excludedTeams[n.name])),e!=null&&e.includeNationalTeams?[...t,...Ph]:t},Rh="gameSettings",Th=({isOpen:e,onClose:t,settings:n,onSettingsChange:r})=>{const[a,l]=y.useState(n);y.useEffect(()=>{l(n)},[n]);const o=i=>{const{name:u,checked:s}=i.target;let m;if(u.startsWith("exclude_")){const f=u.replace("exclude_","");m={...a,excludedTeams:{...a.excludedTeams,[f]:s}}}else m={...a,[u]:s};l(m),r(m),localStorage.setItem(Rh,JSON.stringify(m))};return e?p.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:p.jsxs("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-md p-6 relative",children:[p.jsx("button",{onClick:t,className:"absolute top-4 right-4 text-gray-500 hover:text-gray-700",children:p.jsx(id,{size:20})}),p.jsx("h2",{className:"text-2xl font-bold mb-6 gradient-red-blue inline-block text-transparent bg-clip-text",children:"Game Settings"}),p.jsxs("div",{className:"space-y-6",children:[p.jsxs("div",{className:"flex items-start",children:[p.jsxs("label",{htmlFor:"includeNationalTeams",className:"text-gray-700 mr-2",dir:"rtl",children:[p.jsx("span",{className:"font-medium",children:"إضافة المنتخبات الوطنية"}),p.jsx("div",{className:"text-sm text-gray-500 mt-1",children:"🇩🇪 المانيا ~ 🇪🇸 اسبانيا ~ 🇦🇷 الأرجنتين ~ 🇧🇷 البرازيل ~ 🇫🇷 فرنسا ~ 🇵🇹 البرتغال ~ 🏴󠁧󠁢󠁥󠁮󠁧󠁿 إنجلترا ~ 🇳🇱 هولندا"})]}),p.jsx("input",{type:"checkbox",id:"includeNationalTeams",name:"includeNationalTeams",checked:a.includeNationalTeams,onChange:o,className:"h-7 w-7 text-blue-600 rounded focus:ring-blue-500 mt-1 mr-3"})]}),p.jsxs("div",{className:"space-y-2",children:[p.jsx("div",{className:"flex items-start",children:p.jsxs("label",{className:"text-gray-700 mr-2 w-full",dir:"rtl",children:[p.jsx("span",{className:"font-medium",children:"إستبعاد بعض الفرق"}),p.jsx("div",{className:"text-sm text-gray-500 mt-1",children:"إختر الفرق التى تريد إستبعادها من القائمة."})]})}),p.jsx("div",{className:"grid grid-cols-2 gap-2 mt-2",children:ud.map(i=>{var u;return p.jsxs("div",{className:"flex items-center",children:[p.jsx("input",{type:"checkbox",id:`exclude_${i}`,name:`exclude_${i}`,checked:!!((u=a.excludedTeams)!=null&&u[i]),onChange:o,className:"h-4 w-4 text-blue-600 rounded focus:ring-blue-500 mr-2"}),p.jsx("label",{htmlFor:`exclude_${i}`,className:"text-sm text-gray-700",children:i})]},i)})})]})]}),p.jsx("div",{className:"mt-8 flex justify-center",children:p.jsx("button",{onClick:t,className:"gradient-yellow py-2 px-4 rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300",children:"Ok"})})]})}):null},Mh=[{name:"AFC Bournemouth",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Bournemouth"},{name:"Arsenal FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Arsenal"},{name:"Aston Villa FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Aston Villa"},{name:"Brentford FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Brentford"},{name:"Brighton & Hove",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Brighton"},{name:"Chelsea FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Chelsea"},{name:"Crystal Palace FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Palace"},{name:"Everton FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Everton"},{name:"Fulham FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Fulham"},{name:"Liverpool FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Liverpool"},{name:"Manchester City FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Man City"},{name:"Manchester United FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Man Utd"},{name:"Newcastle United FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Newcastle"},{name:"Nottingham Forest FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Forest"},{name:"Tottenham Hotspur FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Spurs"},{name:"West Ham United FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"West Ham"},{name:"Wolverhampton FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Wolves"},{name:"Ipswich Town FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Ipswich"},{name:"Southampton FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Southampton"},{name:"Leicester City FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Leicester"},{name:"Blackburn Rovers",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Blackburn"},{name:"Burnley FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Burnley"},{name:"Bristol City FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Bristol City"},{name:"Cardiff City FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Cardiff"},{name:"Coventry City FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Coventry"},{name:"Derby County FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Derby"},{name:"Hull City AFC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Hull"},{name:"Luton Town FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Luton"},{name:"Leeds United FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Leeds"},{name:"Middlesbrough FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Boro"},{name:"Millwall FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Millwall"},{name:"Norwich City FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Norwich"},{name:"Oxford United FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Oxford Utd"},{name:"Plymouth Argyle FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Plymouth"},{name:"Portsmouth FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Portsmouth"},{name:"Preston North FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Preston"},{name:"Queens Park Rangers",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"QPR"},{name:"Sheffield Wednesday",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Sheff Wed"},{name:"Sheffield United",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Sheff Utd"},{name:"Stoke City FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Stoke"},{name:"Sunderland",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Sunderland"},{name:"Swansea City",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Swansea"},{name:"Watford FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"Watford"},{name:"West Bromwich Albion FC",league:"🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League 2",shortName:"West Brom"},{name:"Deportivo Alavés",league:"🇪🇸 La Liga",shortName:"Alavés"},{name:"Athletic Bilbao",league:"🇪🇸 La Liga",shortName:"Bilbao"},{name:"CA Osasuna",league:"🇪🇸 La Liga",shortName:"Osasuna"},{name:"Atlético Madrid",league:"🇪🇸 La Liga",shortName:"Atlético"},{name:"FC Barcelona",league:"🇪🇸 La Liga",shortName:"Barcelona"},{name:"Getafe CF",league:"🇪🇸 La Liga",shortName:"Getafe"},{name:"Girona FC",league:"🇪🇸 La Liga",shortName:"Girona"},{name:"Rayo Vallecano",league:"🇪🇸 La Liga",shortName:"Rayo"},{name:"RCD Espanyol",league:"🇪🇸 La Liga",shortName:"Espanyol"},{name:"RCD Mallorca",league:"🇪🇸 La Liga",shortName:"Mallorca"},{name:"RC Celta de Vigo",league:"🇪🇸 La Liga",shortName:"Celta"},{name:"Real Betis",league:"🇪🇸 La Liga",shortName:"Betis"},{name:"Real Madrid CF",league:"🇪🇸 La Liga",shortName:"Madrid"},{name:"Real Sociedad",league:"🇪🇸 La Liga",shortName:"Sociedad"},{name:"Real Valladolid CF",league:"🇪🇸 La Liga",shortName:"Valladolid"},{name:"Sevilla FC",league:"🇪🇸 La Liga",shortName:"Sevilla"},{name:"UD Las Palmas",league:"🇪🇸 La Liga",shortName:"Las Palmas"},{name:"Villarreal CF",league:"🇪🇸 La Liga",shortName:"Villarreal"},{name:"Valencia CF",league:"🇪🇸 La Liga",shortName:"Valencia"},{name:"Leganés",league:"🇪🇸 La Liga",shortName:"Leganés"},{name:"Albacete Balompié",league:"🇪🇸 La Liga 2",shortName:"Albacete"},{name:"Burgos CF",league:"🇪🇸 La Liga 2",shortName:"Burgos"},{name:"Cádiz CF",league:"🇪🇸 La Liga 2",shortName:"Cádiz"},{name:"CD Castellón",league:"🇪🇸 La Liga 2",shortName:"Castellón"},{name:"CD Eldense",league:"🇪🇸 La Liga 2",shortName:"Eldense"},{name:"CD Mirandés",league:"🇪🇸 La Liga 2",shortName:"Mirandés"},{name:"CD Tenerife",league:"🇪🇸 La Liga 2",shortName:"Tenerife"},{name:"Córdoba CF",league:"🇪🇸 La Liga 2",shortName:"Córdoba"},{name:"Deportivo La Coruña",league:"🇪🇸 La Liga 2",shortName:"Deportivo"},{name:"Elche CF",league:"🇪🇸 La Liga 2",shortName:"Elche"},{name:"FC Cartagena",league:"🇪🇸 La Liga 2",shortName:"Cartagena"},{name:"Granada CF",league:"🇪🇸 La Liga 2",shortName:"Granada"},{name:"Levante UD",league:"🇪🇸 La Liga 2",shortName:"Levante"},{name:"Málaga CF",league:"🇪🇸 La Liga 2",shortName:"Málaga"},{name:"Racing Club",league:"🇪🇸 La Liga 2",shortName:"Racing Club"},{name:"Real Oviedo",league:"🇪🇸 La Liga 2",shortName:"Oviedo"},{name:"Real Racing",league:"🇪🇸 La Liga 2",shortName:"Racing"},{name:"Real Sporting de Gijón",league:"🇪🇸 La Liga 2",shortName:"Sporting Gijón"},{name:"Real Zaragoza",league:"🇪🇸 La Liga 2",shortName:"Zaragoza"},{name:"SD Eibar",league:"🇪🇸 La Liga 2",shortName:"Eibar"},{name:"SD Huesca",league:"🇪🇸 La Liga 2",shortName:"Huesca"},{name:"UD Almería",league:"🇪🇸 La Liga 2",shortName:"Almería"},{name:"AC Milan",league:"🇮🇹 Serie A",shortName:"Milan"},{name:"AC Monza",league:"🇮🇹 Serie A",shortName:"Monza"},{name:"ACF Fiorentina",league:"🇮🇹 Serie A",shortName:"Fiorentina"},{name:"AS Roma",league:"🇮🇹 Serie A",shortName:"Roma"},{name:"Atalanta BC",league:"🇮🇹 Serie A",shortName:"Atalanta"},{name:"Bologna FC 1909",league:"🇮🇹 Serie A",shortName:"Bologna"},{name:"Cagliari Calcio",league:"🇮🇹 Serie A",shortName:"Cagliari"},{name:"Como 1907",league:"🇮🇹 Serie A",shortName:"Como"},{name:"Empoli FC",league:"🇮🇹 Serie A",shortName:"Empoli"},{name:"Genoa CFC",league:"🇮🇹 Serie A",shortName:"Genoa"},{name:"Hellas Verona FC",league:"🇮🇹 Serie A",shortName:"Verona"},{name:"Inter Milan",league:"🇮🇹 Serie A",shortName:"Inter"},{name:"Juventus FC",league:"🇮🇹 Serie A",shortName:"Juventus"},{name:"Parma Calcio 1913",league:"🇮🇹 Serie A",shortName:"Parma"},{name:"SS Lazio",league:"🇮🇹 Serie A",shortName:"Lazio"},{name:"SSC Napoli",league:"🇮🇹 Serie A",shortName:"Napoli"},{name:"Torino FC",league:"🇮🇹 Serie A",shortName:"Torino"},{name:"Udinese Calcio",league:"🇮🇹 Serie A",shortName:"Udinese"},{name:"US Lecce",league:"🇮🇹 Serie A",shortName:"Lecce"},{name:"Venezia FC",league:"🇮🇹 Serie A",shortName:"Venezia"},{name:"AC Reggiana 1919",league:"🇮🇹 Serie B",shortName:"Reggiana"},{name:"AS Cittadella",league:"🇮🇹 Serie B",shortName:"Cittadella"},{name:"Brescia Calcio",league:"🇮🇹 Serie B",shortName:"Brescia"},{name:"Carrarese Calcio 1908",league:"🇮🇹 Serie B",shortName:"Carrarese"},{name:"Cesena FC",league:"🇮🇹 Serie B",shortName:"Cesena"},{name:"Cosenza Calcio",league:"🇮🇹 Serie B",shortName:"Cosenza"},{name:"FC Südtirol",league:"🇮🇹 Serie B",shortName:"Südtirol"},{name:"Frosinone Calcio",league:"🇮🇹 Serie B",shortName:"Frosinone"},{name:"Mantova 1911",league:"🇮🇹 Serie B",shortName:"Mantova"},{name:"Modena FC",league:"🇮🇹 Serie B",shortName:"Modena"},{name:"Palermo FC",league:"🇮🇹 Serie B",shortName:"Palermo"},{name:"Pisa Sporting Club",league:"🇮🇹 Serie B",shortName:"Pisa"},{name:"SS Juve Stabia",league:"🇮🇹 Serie B",shortName:"Juve Stabia"},{name:"SSC Bari",league:"🇮🇹 Serie B",shortName:"Bari"},{name:"Spezia Calcio",league:"🇮🇹 Serie B",shortName:"Spezia"},{name:"UC Sampdoria",league:"🇮🇹 Serie B",shortName:"Sampdoria"},{name:"US Catanzaro 1929",league:"🇮🇹 Serie B",shortName:"Catanzaro"},{name:"US Cremonese",league:"🇮🇹 Serie B",shortName:"Cremonese"},{name:"US Salernitana 1919",league:"🇮🇹 Serie B",shortName:"Salernitana"},{name:"US Sassuolo Calcio",league:"🇮🇹 Serie B",shortName:"Sassuolo"},{name:"1. FC Heidenheim",league:"🇩🇪 Bundesliga",shortName:"Heidenheim"},{name:"1. FC Union Berlin",league:"🇩🇪 Bundesliga",shortName:"Union Berlin"},{name:"FSV Mainz 05",league:"🇩🇪 Bundesliga",shortName:"Mainz"},{name:"Bayer 04 Leverkusen",league:"🇩🇪 Bundesliga",shortName:"Leverkusen"},{name:"Borussia Dortmund",league:"🇩🇪 Bundesliga",shortName:"Dortmund"},{name:"Eintracht Frankfurt",league:"🇩🇪 Bundesliga",shortName:"Frankfurt"},{name:"FC Augsburg",league:"🇩🇪 Bundesliga",shortName:"Augsburg"},{name:"FC Bayern München",league:"🇩🇪 Bundesliga",shortName:"Bayern"},{name:"FC St. Pauli",league:"🇩🇪 Bundesliga",shortName:"St. Pauli"},{name:"KSV Holstein",league:"🇩🇪 Bundesliga",shortName:"Holstein Kiel"},{name:"RB Leipzig",league:"🇩🇪 Bundesliga",shortName:"Leipzig"},{name:"SC Freiburg",league:"🇩🇪 Bundesliga",shortName:"Freiburg"},{name:"SV Werder Bremen",league:"🇩🇪 Bundesliga",shortName:"Bremen"},{name:"TSG 1899 Hoffenheim",league:"🇩🇪 Bundesliga",shortName:"Hoffenheim"},{name:"VfB Stuttgart",league:"🇩🇪 Bundesliga",shortName:"Stuttgart"},{name:"VfL Bochum 1848",league:"🇩🇪 Bundesliga",shortName:"Bochum"},{name:"VfL Borussia M’gladbach",league:"🇩🇪 Bundesliga",shortName:"Gladbach"},{name:"VfL Wolfsburg",league:"🇩🇪 Bundesliga",shortName:"Wolfsburg"},{name:"AJ Auxerre",league:"🇫🇷 Ligue 1",shortName:"Auxerre"},{name:"Angers SCO",league:"🇫🇷 Ligue 1",shortName:"Angers"},{name:"AS Monaco",league:"🇫🇷 Ligue 1",shortName:"Monaco"},{name:"AS Saint-Étienne",league:"🇫🇷 Ligue 1",shortName:"St-Étienne"},{name:"FC Nantes",league:"🇫🇷 Ligue 1",shortName:"Nantes"},{name:"Le Havre AC",league:"🇫🇷 Ligue 1",shortName:"Le Havre"},{name:"LOSC Lille",league:"🇫🇷 Ligue 1",shortName:"Lille"},{name:"Montpellier Hérault SC",league:"🇫🇷 Ligue 1",shortName:"Montpellier"},{name:"OGC Nice",league:"🇫🇷 Ligue 1",shortName:"Nice"},{name:"Olympique de Marseille",league:"🇫🇷 Ligue 1",shortName:"Marseille"},{name:"Olympique Lyonnais",league:"🇫🇷 Ligue 1",shortName:"Lyon"},{name:"Paris Saint-Germain",league:"🇫🇷 Ligue 1",shortName:"PSG"},{name:"RC Lens",league:"🇫🇷 Ligue 1",shortName:"Lens"},{name:"RC Strasbourg Alsace",league:"🇫🇷 Ligue 1",shortName:"Strasbourg"},{name:"Stade Brestois 29",league:"🇫🇷 Ligue 1",shortName:"Brest"},{name:"Stade de Reims",league:"🇫🇷 Ligue 1",shortName:"Reims"},{name:"Stade Rennais FC",league:"🇫🇷 Ligue 1",shortName:"Rennes"},{name:"Toulouse FC",league:"🇫🇷 Ligue 1",shortName:"Toulouse"},{name:"AC Ajaccio",league:"🇫🇷 Ligue 2",shortName:"Ajaccio"},{name:"Amiens SC",league:"🇫🇷 Ligue 2",shortName:"Amiens"},{name:"Clermont Foot 63",league:"🇫🇷 Ligue 2",shortName:"Clermont"},{name:"En Avant Guingamp",league:"🇫🇷 Ligue 2",shortName:"Guingamp"},{name:"ESTAC Troyes",league:"🇫🇷 Ligue 2",shortName:"Troyes"},{name:"FC Annecy",league:"🇫🇷 Ligue 2",shortName:"Annecy"},{name:"FC Lorient",league:"🇫🇷 Ligue 2",shortName:"Lorient"},{name:"FC Martigues",league:"🇫🇷 Ligue 2",shortName:"Martigues"},{name:"FC Metz",league:"🇫🇷 Ligue 2",shortName:"Metz"},{name:"FC Sochaux-Montbéliard",league:"🇫🇷 Ligue 2",shortName:"Sochaux"},{name:"Grenoble Foot 38",league:"🇫🇷 Ligue 2",shortName:"Grenoble"},{name:"Paris FC",league:"🇫🇷 Ligue 2",shortName:"Paris FC"},{name:"Pau FC",league:"🇫🇷 Ligue 2",shortName:"Pau"},{name:"Red Star FC",league:"🇫🇷 Ligue 2",shortName:"Red Star"},{name:"Rodez Aveyron Football",league:"🇫🇷 Ligue 2",shortName:"Rodez"},{name:"SC Bastia",league:"🇫🇷 Ligue 2",shortName:"Bastia"},{name:"Stade Lavallois MFC",league:"🇫🇷 Ligue 2",shortName:"Laval"},{name:"Stade Malherbe Caen",league:"🇫🇷 Ligue 2",shortName:"Caen"},{name:"US Concarneau",league:"🇫🇷 Ligue 2",shortName:"Concarneau"},{name:"US L Dunkerque",league:"🇫🇷 Ligue 2",shortName:"Dunkerque"},{name:"US Quevilly Rouen",league:"🇫🇷 Ligue 2",shortName:"Quevilly"},{name:"Valenciennes FC",league:"🇫🇷 Ligue 2",shortName:"Valenciennes"},{name:"AFC Ajax",league:"🇳🇱 Eredivisie",shortName:"Ajax"},{name:"Almere City FC",league:"🇳🇱 Eredivisie",shortName:"Almere"},{name:"AZ Alkmaar",league:"🇳🇱 Eredivisie",shortName:"AZ"},{name:"FC Groningen",league:"🇳🇱 Eredivisie",shortName:"Groningen"},{name:"FC Twente Enschede",league:"🇳🇱 Eredivisie",shortName:"Twente"},{name:"FC Utrecht",league:"🇳🇱 Eredivisie",shortName:"Utrecht"},{name:"Feyenoord Rotterdam",league:"🇳🇱 Eredivisie",shortName:"Feyenoord"},{name:"Fortuna Sittard",league:"🇳🇱 Eredivisie",shortName:"Fortuna"},{name:"Go Ahead Eagles",league:"🇳🇱 Eredivisie",shortName:"Go Ahead"},{name:"Heracles Almelo",league:"🇳🇱 Eredivisie",shortName:"Heracles"},{name:"NAC Breda",league:"🇳🇱 Eredivisie",shortName:"NAC Breda"},{name:"N.E.C. Nijmegen",league:"🇳🇱 Eredivisie",shortName:"NEC"},{name:"PEC Zwolle",league:"🇳🇱 Eredivisie",shortName:"Zwolle"},{name:"PSV Eindhoven",league:"🇳🇱 Eredivisie",shortName:"PSV"},{name:"RKC Waalwijk",league:"🇳🇱 Eredivisie",shortName:"Waalwijk"},{name:"SC Heerenveen",league:"🇳🇱 Eredivisie",shortName:"Heerenveen"},{name:"Sparta Rotterdam",league:"🇳🇱 Eredivisie",shortName:"Sparta"},{name:"Willem II Tilburg",league:"🇳🇱 Eredivisie",shortName:"Willem II"},{name:"AVS Futebol",league:"🇵🇹 Primeira Liga",shortName:"AVS"},{name:"Boavista FC",league:"🇵🇹 Primeira Liga",shortName:"Boavista"},{name:"Casa Pia AC",league:"🇵🇹 Primeira Liga",shortName:"Casa Pia"},{name:"CD Santa Clara",league:"🇵🇹 Primeira Liga",shortName:"Santa Clara"},{name:"CF Estrela da Amadora",league:"🇵🇹 Primeira Liga",shortName:"Estrela"},{name:"FC Arouca",league:"🇵🇹 Primeira Liga",shortName:"Arouca"},{name:"FC Famalicão",league:"🇵🇹 Primeira Liga",shortName:"Famalicão"},{name:"FC Porto",league:"🇵🇹 Primeira Liga",shortName:"Porto"},{name:"GD Estoril Praia",league:"🇵🇹 Primeira Liga",shortName:"Estoril"},{name:"Gil Vicente FC",league:"🇵🇹 Primeira Liga",shortName:"Gil Vicente"},{name:"Moreirense FC",league:"🇵🇹 Primeira Liga",shortName:"Moreirense"},{name:"Rio Ave FC",league:"🇵🇹 Primeira Liga",shortName:"Rio Ave"},{name:"SC Farense",league:"🇵🇹 Primeira Liga",shortName:"Farense"},{name:"SL Benfica",league:"🇵🇹 Primeira Liga",shortName:"Benfica"},{name:"Sporting Braga",league:"🇵🇹 Primeira Liga",shortName:"Braga"},{name:"Sporting CP",league:"🇵🇹 Primeira Liga",shortName:"Sporting"},{name:"Vitória SC",league:"🇵🇹 Primeira Liga",shortName:"Vitória SC"},{name:"América FC MG",league:"🇧🇷 Brazilian",shortName:"América MG"},{name:"Amazonas FC",league:"🇧🇷 Brazilian",shortName:"Amazonas"},{name:"Athletico Paranaense",league:"🇧🇷 Brazilian",shortName:"Athletico-PR"},{name:"Atlético Goianiense",league:"🇧🇷 Brazilian",shortName:"Atlético-GO"},{name:"Atlético Mineiro",league:"🇧🇷 Brazilian",shortName:"Atlético-MG"},{name:"Avaí FC",league:"🇧🇷 Brazilian",shortName:"Avaí"},{name:"Botafogo FR",league:"🇧🇷 Brazilian",shortName:"Botafogo"},{name:"Botafogo SP",league:"🇧🇷 Brazilian",shortName:"Botafogo-SP"},{name:"Ceará SC",league:"🇧🇷 Brazilian",shortName:"Ceará"},{name:"Chapecoense AF",league:"🇧🇷 Brazilian",shortName:"Chapecoense"},{name:"Clube de Regatas Brasil",league:"🇧🇷 Brazilian",shortName:"CRB"},{name:"Clube do Remo",league:"🇧🇷 Brazilian",shortName:"Remo"},{name:"Coritiba FBC",league:"🇧🇷 Brazilian",shortName:"Coritiba"},{name:"CR Flamengo",league:"🇧🇷 Brazilian",shortName:"Flamengo"},{name:"CR Vasco da Gama",league:"🇧🇷 Brazilian",shortName:"Vasco"},{name:"Criciúma EC",league:"🇧🇷 Brazilian",shortName:"Criciúma"},{name:"Cruzeiro EC",league:"🇧🇷 Brazilian",shortName:"Cruzeiro"},{name:"Cuiabá EC",league:"🇧🇷 Brazilian",shortName:"Cuiabá"},{name:"EC Bahia",league:"🇧🇷 Brazilian",shortName:"Bahia"},{name:"EC Juventude",league:"🇧🇷 Brazilian",shortName:"Juventude"},{name:"EC Vitória",league:"🇧🇷 Brazilian",shortName:"Vitória"},{name:"Ferroviária",league:"🇧🇷 Brazilian",shortName:"Ferroviária"},{name:"Fluminense FC",league:"🇧🇷 Brazilian",shortName:"Fluminense"},{name:"Fortaleza EC",league:"🇧🇷 Brazilian",shortName:"Fortaleza"},{name:"Goiás EC",league:"🇧🇷 Brazilian",shortName:"Goiás"},{name:"Grêmio FBPA",league:"🇧🇷 Brazilian",shortName:"Grêmio"},{name:"Grêmio Novorizontino",league:"🇧🇷 Brazilian",shortName:"Novorizontino"},{name:"Mirassol FC",league:"🇧🇷 Brazilian",shortName:"Mirassol"},{name:"Operário Ferroviário EC",league:"🇧🇷 Brazilian",shortName:"Operário-PR"},{name:"Paysandu SC",league:"🇧🇷 Brazilian",shortName:"Paysandu"},{name:"Red Bull Bragantino",league:"🇧🇷 Brazilian",shortName:"Bragantino"},{name:"Santos FC",league:"🇧🇷 Brazilian",shortName:"Santos"},{name:"São Paulo FC",league:"🇧🇷 Brazilian",shortName:"São Paulo"},{name:"SC Corinthians Paulista",league:"🇧🇷 Brazilian",shortName:"Corinthians"},{name:"SC Internacional",league:"🇧🇷 Brazilian",shortName:"Internacional"},{name:"SE Palmeiras",league:"🇧🇷 Brazilian",shortName:"Palmeiras"},{name:"Sport Recife",league:"🇧🇷 Brazilian",shortName:"Sport"},{name:"Vila Nova FC",league:"🇧🇷 Brazilian",shortName:"Vila Nova"},{name:"Volta Redonda FC",league:"🇧🇷 Brazilian",shortName:"Volta Redonda"},{name:"Argentinos Juniors",league:"🇦🇷 Argentine Primera División",shortName:"Argentinos"},{name:"CA Aldosivi",league:"🇦🇷 Argentine Primera División",shortName:"Aldosivi"},{name:"CA Banfield",league:"🇦🇷 Argentine Primera División",shortName:"Banfield"},{name:"CA Barracas Central",league:"🇦🇷 Argentine Primera División",shortName:"Barracas"},{name:"CA Belgrano",league:"🇦🇷 Argentine Primera División",shortName:"Belgrano"},{name:"CA Boca Juniors",league:"🇦🇷 Argentine Primera División",shortName:"Boca"},{name:"CA Central Córdoba SdE",league:"🇦🇷 Argentine Primera División",shortName:"Central Cba"},{name:"CA Huracán",league:"🇦🇷 Argentine Primera División",shortName:"Huracán"},{name:"CA Independiente",league:"🇦🇷 Argentine Primera División",shortName:"Independiente"},{name:"CA Lanús",league:"🇦🇷 Argentine Primera División",shortName:"Lanús"},{name:"CA Newell's Old Boys",league:"🇦🇷 Argentine Primera División",shortName:"Newell's"},{name:"CA Platense",league:"🇦🇷 Argentine Primera División",shortName:"Platense"},{name:"CA River Plate",league:"🇦🇷 Argentine Primera División",shortName:"River"},{name:"CA Rosario Central",league:"🇦🇷 Argentine Primera División",shortName:"Rosario"},{name:"CA San Lorenzo Almagro",league:"🇦🇷 Argentine Primera División",shortName:"San Lorenzo"},{name:"CA San Martín de San Juan",league:"🇦🇷 Argentine Primera División",shortName:"San Martín SJ"},{name:"CA Sarmiento",league:"🇦🇷 Argentine Primera División",shortName:"Sarmiento"},{name:"CA Talleres de Córdoba",league:"🇦🇷 Argentine Primera División",shortName:"Talleres"},{name:"CA Tigre",league:"🇦🇷 Argentine Primera División",shortName:"Tigre"},{name:"CA Unión de Santa Fe",league:"🇦🇷 Argentine Primera División",shortName:"Unión"},{name:"CA Vélez Sarsfield",league:"🇦🇷 Argentine Primera División",shortName:"Vélez"},{name:"Club Atlético Tucumán",league:"🇦🇷 Argentine Primera División",shortName:"Atl. Tucumán"},{name:"Club Estudiantes LP",league:"🇦🇷 Argentine Primera División",shortName:"Estudiantes"},{name:"CSD Defensa y Justicia",league:"🇦🇷 Argentine Primera División",shortName:"Defensa"},{name:"CD Godoy Cruz",league:"🇦🇷 Argentine Primera División",shortName:"Godoy Cruz"},{name:"Deportivo Riestra AFBC",league:"🇦🇷 Argentine Primera División",shortName:"Riestra"},{name:"Gimnasia y Esgrima LP",league:"🇦🇷 Argentine Primera División",shortName:"Gimnasia"},{name:"Independiente Rivadavia",league:"🇦🇷 Argentine Primera División",shortName:"Ind. Rivadavia"},{name:"Instituto ACC",league:"🇦🇷 Argentine Primera División",shortName:"Instituto"},{name:"Racing Club Avellaneda",league:"🇦🇷 Argentine Primera División",shortName:"Racing"},{name:"FC Salzburg",league:"🇦🇹 Austrian 🇩🇪 Bundesliga",shortName:"Salzburg"},{name:"SK Sturm Graz",league:"🇦🇹 Austrian 🇩🇪 Bundesliga",shortName:"Sturm Graz"},{name:"Cercle Brugge KSV",league:"🇧🇪 Belgian Pro League",shortName:"Cercle Brugge"},{name:"Club Brugge KV",league:"🇧🇪 Belgian Pro League",shortName:"Club Brugge"},{name:"FCV Dender EH",league:"🇧🇪 Belgian Pro League",shortName:"Dender"},{name:"K Beerschot VA",league:"🇧🇪 Belgian Pro League",shortName:"Beerschot"},{name:"KAA Gent",league:"🇧🇪 Belgian Pro League",shortName:"Gent"},{name:"KRC Genk",league:"🇧🇪 Belgian Pro League",shortName:"Genk"},{name:"KV Kortrijk",league:"🇧🇪 Belgian Pro League",shortName:"Kortrijk"},{name:"KV Mechelen",league:"🇧🇪 Belgian Pro League",shortName:"Mechelen"},{name:"KVC Westerlo",league:"🇧🇪 Belgian Pro League",shortName:"Westerlo"},{name:"Oud-Heverlee Leuven",league:"🇧🇪 Belgian Pro League",shortName:"OH Leuven"},{name:"R. Union Saint-Gilloise",league:"🇧🇪 Belgian Pro League",shortName:"Union SG"},{name:"Royal Antwerp FC",league:"🇧🇪 Belgian Pro League",shortName:"Antwerp"},{name:"Royal Charleroi SC",league:"🇧🇪 Belgian Pro League",shortName:"Charleroi"},{name:"Royal Standard de Liège",league:"🇧🇪 Belgian Pro League",shortName:"Standard"},{name:"Sint-Truidense VV",league:"🇧🇪 Belgian Pro League",shortName:"St. Truiden"},{name:"Audax Italiano",league:"🇨🇱 Chilean Primera División",shortName:"Audax"},{name:"CD Cobresal",league:"🇨🇱 Chilean Primera División",shortName:"Cobresal"},{name:"CD Coquimbo Unido",league:"🇨🇱 Chilean Primera División",shortName:"Coquimbo"},{name:"CD Huachipato",league:"🇨🇱 Chilean Primera División",shortName:"Huachipato"},{name:"CD Limache",league:"🇨🇱 Chilean Primera División",shortName:"Limache"},{name:"CD Palestino",league:"🇨🇱 Chilean Primera División",shortName:"Palestino"},{name:"CD Universidad Católica",league:"🇨🇱 Chilean Primera División",shortName:"U. Católica"},{name:"CSD Colo-Colo",league:"🇨🇱 Chilean Primera División",shortName:"Colo-Colo"},{name:"Deportes Iquique",league:"🇨🇱 Chilean Primera División",shortName:"Iquique"},{name:"Deportes La Serena",league:"🇨🇱 Chilean Primera División",shortName:"La Serena"},{name:"Deportivo Ñublense",league:"🇨🇱 Chilean Primera División",shortName:"Ñublense"},{name:"Everton de Viña del Mar",league:"🇨🇱 Chilean Primera División",shortName:"Everton VM"},{name:"O'Higgins FC",league:"🇨🇱 Chilean Primera División",shortName:"O'Higgins"},{name:"CD Unión La Calera",league:"🇨🇱 Chilean Primera División",shortName:"La Calera"},{name:"Unión Española",league:"🇨🇱 Chilean Primera División",shortName:"U. Española"},{name:"Universidad de Chile",league:"🇨🇱 Chilean Primera División",shortName:"U. de Chile"},{name:"Shandong Taishan FC",league:"🇨🇳 Chinese Super League",shortName:"Shandong"},{name:"Shanghai Port FC",league:"🇨🇳 Chinese Super League",shortName:"Shanghai Port"},{name:"Shanghai Shenhua FC",league:"🇨🇳 Chinese Super League",shortName:"Shanghai Shenhua"},{name:"Zhejiang FC",league:"🇨🇳 Chinese Super League",shortName:"Zhejiang"},{name:"Águilas Doradas Rionegro",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Águilas Doradas"},{name:"Alianza Petrolera FC",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Alianza"},{name:"América de Cali",league:"🇨🇴 Colombian Categoría Primera A",shortName:"América Cali"},{name:"Atlético Nacional",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Nacional"},{name:"Boyacá Chicó FC",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Boyacá Chicó"},{name:"Club Atlético Bucaramanga",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Bucaramanga"},{name:"Club Deportes Tolima",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Tolima"},{name:"Club Independiente Santa Fe",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Santa Fe"},{name:"Club Unión Magdalena",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Unión Magdalena"},{name:"CD La Equidad Seguros",league:"🇨🇴 Colombian Categoría Primera A",shortName:"La Equidad"},{name:"CD Popular Junior FC",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Junior"},{name:"Deportivo Cali",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Deportivo Cali"},{name:"Deportivo Pasto",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Pasto"},{name:"Deportivo Pereira",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Pereira"},{name:"Envigado FC",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Envigado"},{name:"Fortaleza CEIF",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Fortaleza CEIF"},{name:"Independiente Medellín",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Medellín"},{name:"Llaneros FC",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Llaneros"},{name:"Millonarios FC",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Millonarios"},{name:"Once Caldas",league:"🇨🇴 Colombian Categoría Primera A",shortName:"Once Caldas"},{name:"GNK Dinamo Zagreb",league:"🇭🇷 Croatian Football League",shortName:"Dinamo Zagreb"},{name:"AC Sparta Praha",league:"🇨🇿 Czech First League",shortName:"Sparta Praha"},{name:"FC Viktoria Plzeň",league:"🇨🇿 Czech First League",shortName:"Viktoria Plzeň"},{name:"SK Slavia Praha",league:"🇨🇿 Czech First League",shortName:"Slavia Praha"},{name:"AaB Fodbold",league:"🇩🇰 Danish Superliga",shortName:"AaB"},{name:"Aarhus GF",league:"🇩🇰 Danish Superliga",shortName:"AGF"},{name:"Brøndby IF",league:"🇩🇰 Danish Superliga",shortName:"Brøndby"},{name:"F.C. København",league:"🇩🇰 Danish Superliga",shortName:"København"},{name:"FC Midtjylland",league:"🇩🇰 Danish Superliga",shortName:"Midtjylland"},{name:"FC Nordsjælland",league:"🇩🇰 Danish Superliga",shortName:"Nordsjælland"},{name:"Lyngby Boldklub",league:"🇩🇰 Danish Superliga",shortName:"Lyngby"},{name:"Randers FC",league:"🇩🇰 Danish Superliga",shortName:"Randers"},{name:"Silkeborg IF",league:"🇩🇰 Danish Superliga",shortName:"Silkeborg"},{name:"Sønderjyske Fodbold",league:"🇩🇰 Danish Superliga",shortName:"Sønderjyske"},{name:"Vejle BK",league:"🇩🇰 Danish Superliga",shortName:"Vejle"},{name:"Viborg FF",league:"🇩🇰 Danish Superliga",shortName:"Viborg"},{name:"Olympiacos FC",league:"🇬🇷 Greek Super League",shortName:"Olympiacos"},{name:"P.A.O.K. FC",league:"🇬🇷 Greek Super League",shortName:"PAOK"},{name:"Albirex Niigata",league:"🇯🇵 J.League",shortName:"Niigata"},{name:"Avispa Fukuoka",league:"🇯🇵 J.League",shortName:"Fukuoka"},{name:"Cerezo Osaka",league:"🇯🇵 J.League",shortName:"Cerezo"},{name:"FC Machida Zelvia",league:"🇯🇵 J.League",shortName:"Machida"},{name:"FC Tokyo",league:"🇯🇵 J.League",shortName:"FC Tokyo"},{name:"Fagiano Okayama",league:"🇯🇵 J.League",shortName:"Okayama"},{name:"Gamba Osaka",league:"🇯🇵 J.League",shortName:"Gamba"},{name:"Kashima Antlers",league:"🇯🇵 J.League",shortName:"Kashima"},{name:"Kashiwa Reysol",league:"🇯🇵 J.League",shortName:"Kashiwa"},{name:"Kawasaki Frontale",league:"🇯🇵 J.League",shortName:"Kawasaki"},{name:"Kyoto Sanga FC",league:"🇯🇵 J.League",shortName:"Kyoto"},{name:"Nagoya Grampus",league:"🇯🇵 J.League",shortName:"Nagoya"},{name:"Sanfrecce Hiroshima",league:"🇯🇵 J.League",shortName:"Sanfrecce"},{name:"Shimizu S-Pulse",league:"🇯🇵 J.League",shortName:"Shimizu"},{name:"Shonan Bellmare",league:"🇯🇵 J.League",shortName:"Shonan"},{name:"Tokyo Verdy",league:"🇯🇵 J.League",shortName:"Verdy"},{name:"Urawa Reds",league:"🇯🇵 J.League",shortName:"Urawa"},{name:"Vissel Kobe",league:"🇯🇵 J.League",shortName:"Kobe"},{name:"Yokohama F. Marinos",league:"🇯🇵 J.League",shortName:"Marinos"},{name:"Yokohama FC",league:"🇯🇵 J.League",shortName:"Yokohama FC"},{name:"Atlanta United FC",league:"🇺🇸 MLS",shortName:"Atlanta Utd"},{name:"Austin FC",league:"🇺🇸 MLS",shortName:"Austin"},{name:"CF Montréal",league:"🇺🇸 MLS",shortName:"Montréal"},{name:"Charlotte FC",league:"🇺🇸 MLS",shortName:"Charlotte"},{name:"Chicago Fire FC",league:"🇺🇸 MLS",shortName:"Chicago"},{name:"Colorado Rapids",league:"🇺🇸 MLS",shortName:"Colorado"},{name:"Columbus Crew",league:"🇺🇸 MLS",shortName:"Columbus"},{name:"D.C. United",league:"🇺🇸 MLS",shortName:"D.C. United"},{name:"FC Cincinnati",league:"🇺🇸 MLS",shortName:"Cincinnati"},{name:"FC Dallas",league:"🇺🇸 MLS",shortName:"Dallas"},{name:"Houston Dynamo FC",league:"🇺🇸 MLS",shortName:"Houston"},{name:"Inter Miami CF",league:"🇺🇸 MLS",shortName:"Inter Miami"},{name:"Los Angeles FC",league:"🇺🇸 MLS",shortName:"LAFC"},{name:"Los Angeles Galaxy",league:"🇺🇸 MLS",shortName:"LA Galaxy"},{name:"Minnesota United FC",league:"🇺🇸 MLS",shortName:"Minnesota"},{name:"Nashville SC",league:"🇺🇸 MLS",shortName:"Nashville"},{name:"New England Revolution",league:"🇺🇸 MLS",shortName:"New England"},{name:"New York City FC",league:"🇺🇸 MLS",shortName:"NYCFC"},{name:"New York Red Bulls",league:"🇺🇸 MLS",shortName:"NY Red Bulls"},{name:"Orlando City SC",league:"🇺🇸 MLS",shortName:"Orlando"},{name:"Philadelphia Union",league:"🇺🇸 MLS",shortName:"Philadelphia"},{name:"Portland Timbers",league:"🇺🇸 MLS",shortName:"Portland"},{name:"Real Salt Lake",league:"🇺🇸 MLS",shortName:"Salt Lake"},{name:"San Diego FC",league:"🇺🇸 MLS",shortName:"San Diego"},{name:"San Jose Earthquakes",league:"🇺🇸 MLS",shortName:"San Jose"},{name:"Seattle Sounders FC",league:"🇺🇸 MLS",shortName:"Seattle"},{name:"Sporting Kansas City",league:"🇺🇸 MLS",shortName:"Sporting KC"},{name:"St. Louis City SC",league:"🇺🇸 MLS",shortName:"St. Louis"},{name:"Toronto FC",league:"🇺🇸 MLS",shortName:"Toronto"},{name:"Vancouver Whitecaps FC",league:"🇺🇸 MLS",shortName:"Vancouver"},{name:"Atlas FC",league:"🇲🇽 Liga MX",shortName:"Atlas"},{name:"Atlético de San Luis",league:"🇲🇽 Liga MX",shortName:"San Luis"},{name:"CD Guadalajara",league:"🇲🇽 Liga MX",shortName:"Chivas"},{name:"CF Cruz Azul",league:"🇲🇽 Liga MX",shortName:"Cruz Azul"},{name:"CF Monterrey",league:"🇲🇽 Liga MX",shortName:"Monterrey"},{name:"CF Pachuca",league:"🇲🇽 Liga MX",shortName:"Pachuca"},{name:"Club América",league:"🇲🇽 Liga MX",shortName:"América"},{name:"Club León",league:"🇲🇽 Liga MX",shortName:"León"},{name:"Club Necaxa",league:"🇲🇽 Liga MX",shortName:"Necaxa"},{name:"Club Puebla",league:"🇲🇽 Liga MX",shortName:"Puebla"},{name:"Club Santos Laguna",league:"🇲🇽 Liga MX",shortName:"Santos"},{name:"Club Tijuana",league:"🇲🇽 Liga MX",shortName:"Tijuana"},{name:"FC Juárez",league:"🇲🇽 Liga MX",shortName:"Juárez"},{name:"Mazatlán FC",league:"🇲🇽 Liga MX",shortName:"Mazatlán"},{name:"Pumas UNAM",league:"🇲🇽 Liga MX",shortName:"Pumas"},{name:"Querétaro FC",league:"🇲🇽 Liga MX",shortName:"Querétaro"},{name:"Tigres UANL",league:"🇲🇽 Liga MX",shortName:"Tigres"},{name:"Toluca FC",league:"🇲🇽 Liga MX",shortName:"Toluca"},{name:"FC Akhmat Grozny",league:"🇷🇺 Russian",shortName:"Akhmat"},{name:"FC Akron Tolyatti",league:"🇷🇺 Russian",shortName:"Akron"},{name:"FC Dynamo Makhachkala",league:"🇷🇺 Russian",shortName:"Dynamo M."},{name:"FC Dynamo Moscow",league:"🇷🇺 Russian",shortName:"Dynamo Mos."},{name:"FC Fakel Voronezh",league:"🇷🇺 Russian",shortName:"Fakel"},{name:"FC Khimki",league:"🇷🇺 Russian",shortName:"Khimki"},{name:"FC Krasnodar",league:"🇷🇺 Russian",shortName:"Krasnodar"},{name:"FC Lokomotiv Moscow",league:"🇷🇺 Russian",shortName:"Lokomotiv"},{name:"FC Orenburg",league:"🇷🇺 Russian",shortName:"Orenburg"},{name:"FC Pari Nizhny Novgorod",league:"🇷🇺 Russian",shortName:"Pari NN"},{name:"FC Rostov",league:"🇷🇺 Russian",shortName:"Rostov"},{name:"FC Rubin Kazan",league:"🇷🇺 Russian",shortName:"Rubin"},{name:"FC Spartak Moscow",league:"🇷🇺 Russian",shortName:"Spartak"},{name:"FC Zenit Saint Petersburg",league:"🇷🇺 Russian",shortName:"Zenit"},{name:"PFC CSKA Moscow",league:"🇷🇺 Russian",shortName:"CSKA"},{name:"PFC Krylia Sovetov Samara",league:"🇷🇺 Russian",shortName:"Krylia"},{name:"Aberdeen FC",league:"🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scottish Premiership",shortName:"Aberdeen"},{name:"Celtic FC",league:"🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scottish Premiership",shortName:"Celtic"},{name:"Dundee FC",league:"🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scottish Premiership",shortName:"Dundee"},{name:"Dundee United FC",league:"🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scottish Premiership",shortName:"Dundee Utd"},{name:"Heart of Midlothian FC",league:"🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scottish Premiership",shortName:"Hearts"},{name:"Hibernian FC",league:"🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scottish Premiership",shortName:"Hibs"},{name:"Kilmarnock FC",league:"🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scottish Premiership",shortName:"Kilmarnock"},{name:"Motherwell FC",league:"🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scottish Premiership",shortName:"Motherwell"},{name:"Rangers FC",league:"🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scottish Premiership",shortName:"Rangers"},{name:"Ross County FC",league:"🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scottish Premiership",shortName:"Ross County"},{name:"St. Johnstone FC",league:"🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scottish Premiership",shortName:"St. Johnstone"},{name:"St. Mirren FC",league:"🏴󠁧󠁢󠁳󠁣󠁴󠁿 Scottish Premiership",shortName:"St. Mirren"},{name:"FK Crvena zvezda",league:"🇷🇸 Serbian SuperLiga",shortName:"Red Star"},{name:"ŠK Slovan Bratislava",league:"🇸🇰 Slovak Super Liga",shortName:"Slovan Br."},{name:"Daejeon Hana Citizen FC",league:"🇰🇷 K League",shortName:"Daejeon"},{name:"Daegu FC",league:"🇰🇷 K League",shortName:"Daegu"},{name:"FC Anyang",league:"🇰🇷 K League",shortName:"Anyang"},{name:"FC Seoul",league:"🇰🇷 K League",shortName:"Seoul"},{name:"Gangwon FC",league:"🇰🇷 K League",shortName:"Gangwon"},{name:"Gimcheon Sangmu FC",league:"🇰🇷 K League",shortName:"Gimcheon"},{name:"Gwangju FC",league:"🇰🇷 K League",shortName:"Gwangju"},{name:"Jeju SK FC",league:"🇰🇷 K League",shortName:"Jeju Utd"},{name:"Jeonbuk Hyundai Motors",league:"🇰🇷 K League",shortName:"Jeonbuk"},{name:"Pohang Steelers",league:"🇰🇷 K League",shortName:"Pohang"},{name:"Suwon FC",league:"🇰🇷 K League",shortName:"Suwon FC"},{name:"Ulsan HD",league:"🇰🇷 K League",shortName:"Ulsan"},{name:"BSC Young Boys",league:"🇨🇭 Swiss Super League",shortName:"Young Boys"},{name:"FC Basel 1893",league:"🇨🇭 Swiss Super League",shortName:"Basel"},{name:"FC Lausanne-Sport",league:"🇨🇭 Swiss Super League",shortName:"Lausanne"},{name:"FC Lugano",league:"🇨🇭 Swiss Super League",shortName:"Lugano"},{name:"FC Luzern",league:"🇨🇭 Swiss Super League",shortName:"Luzern"},{name:"FC Sion",league:"🇨🇭 Swiss Super League",shortName:"Sion"},{name:"FC St. Gallen 1879",league:"🇨🇭 Swiss Super League",shortName:"St. Gallen"},{name:"FC Winterthur",league:"🇨🇭 Swiss Super League",shortName:"Winterthur"},{name:"FC Zürich",league:"🇨🇭 Swiss Super League",shortName:"Zürich"},{name:"Grasshopper Club Zürich",league:"🇨🇭 Swiss Super League",shortName:"Grasshoppers"},{name:"Servette FC",league:"🇨🇭 Swiss Super League",shortName:"Servette"},{name:"Yverdon Sport FC",league:"🇨🇭 Swiss Super League",shortName:"Yverdon"},{name:"Adana Demirspor",league:"🇹🇷 Turkish Süper Lig",shortName:"Adana Demir."},{name:"Alanyaspor",league:"🇹🇷 Turkish Süper Lig",shortName:"Alanyaspor"},{name:"Antalyaspor",league:"🇹🇷 Turkish Süper Lig",shortName:"Antalyaspor"},{name:"Beşiktaş JK",league:"🇹🇷 Turkish Süper Lig",shortName:"Beşiktaş"},{name:"Çaykur Rizespor",league:"🇹🇷 Turkish Süper Lig",shortName:"Rizespor"},{name:"Eyüpspor",league:"🇹🇷 Turkish Süper Lig",shortName:"Eyüpspor"},{name:"Fenerbahçe SK",league:"🇹🇷 Turkish Süper Lig",shortName:"Fenerbahçe"},{name:"Galatasaray SK",league:"🇹🇷 Turkish Süper Lig",shortName:"Galatasaray"},{name:"Gaziantep FK",league:"🇹🇷 Turkish Süper Lig",shortName:"Gaziantep"},{name:"Göztepe SK",league:"🇹🇷 Turkish Süper Lig",shortName:"Göztepe"},{name:"Hatayspor",league:"🇹🇷 Turkish Süper Lig",shortName:"Hatayspor"},{name:"İstanbul Başakşehir FK",league:"🇹🇷 Turkish Süper Lig",shortName:"Başakşehir"},{name:"Kasımpaşa SK",league:"🇹🇷 Turkish Süper Lig",shortName:"Kasımpaşa"},{name:"Kayserispor",league:"🇹🇷 Turkish Süper Lig",shortName:"Kayserispor"},{name:"Konyaspor",league:"🇹🇷 Turkish Süper Lig",shortName:"Konyaspor"},{name:"Samsunspor",league:"🇹🇷 Turkish Süper Lig",shortName:"Samsunspor"},{name:"Sipay Bodrum FK",league:"🇹🇷 Turkish Süper Lig",shortName:"Bodrumspor"},{name:"Sivasspor",league:"🇹🇷 Turkish Süper Lig",shortName:"Sivasspor"},{name:"Trabzonspor",league:"🇹🇷 Turkish Süper Lig",shortName:"Trabzonspor"},{name:"FC Shakhtar Donetsk",league:"🇺🇦 Ukrainian 🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League",shortName:"Shakhtar"},{name:"Air Force Club",league:"🌏أندية أخرى (آسيا)",shortName:"Air Force"},{name:"Al Ahli Saudi FC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Ahli (KSA)"},{name:"Al Ain FC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Ain"},{name:"Al Ettifaq FC",league:"🌏أندية أخرى (آسيا)",shortName:"Ettifaq"},{name:"Al Gharafa SC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Gharafa"},{name:"Al Hilal SFC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Hilal"},{name:"Al Ittihad",league:"🌏أندية أخرى (آسيا)",shortName:"Al Ittihad"},{name:"Al Khaldiya SC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Khaldiya"},{name:"Al Kuwait SC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Kuwait"},{name:"Al Nassr FC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Nassr"},{name:"Al Rayyan SC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Rayyan"},{name:"Al Sadd SC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Sadd"},{name:"Al Shorta SC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Shorta"},{name:"Al Taawoun FC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Taawoun"},{name:"Al Wakrah SC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Wakrah"},{name:"Al Wasl FC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Wasl"},{name:"Al Wehdat SC",league:"🌏أندية أخرى (آسيا)",shortName:"Al Wehdat"},{name:"Al-Hussein SC",league:"🌏أندية أخرى (آسيا)",shortName:"Al-Hussein"},{name:"Altyn Asyr FK",league:"🌏أندية أخرى (آسيا)",shortName:"Altyn Asyr"},{name:"BG Pathum United FC",league:"🌏أندية أخرى (آسيا)",shortName:"Pathum Utd"},{name:"Buriram United FC",league:"🌏أندية أخرى (آسيا)",shortName:"Buriram"},{name:"Dynamic Herb Cebu FC",league:"🌏أندية أخرى (آسيا)",shortName:"Cebu"},{name:"Eastern SC",league:"🌏أندية أخرى (آسيا)",shortName:"Eastern"},{name:"Esteghlal FC",league:"🌏أندية أخرى (آسيا)",shortName:"Esteghlal"},{name:"FC Istiklol",league:"🌏أندية أخرى (آسيا)",shortName:"Istiklol"},{name:"FC Nasaf",league:"🌏أندية أخرى (آسيا)",shortName:"Nasaf"},{name:"Johor Darul Ta'zim FC",league:"🌏أندية أخرى (آسيا)",shortName:"JDT"},{name:"Kaya FC-Iloilo",league:"🌏أندية أخرى (آسيا)",shortName:"Kaya"},{name:"Khonkaen United FC",league:"🌏أندية أخرى (آسيا)",shortName:"Khonkaen"},{name:"Lamphun Warriors FC",league:"🌏أندية أخرى (آسيا)",shortName:"Lamphun"},{name:"Lee Man FC",league:"🌏أندية أخرى (آسيا)",shortName:"Lee Man"},{name:"Lion City Sailors FC",league:"🌏أندية أخرى (آسيا)",shortName:"Lion City"},{name:"Mohun Bagan Super Giant",league:"🌏أندية أخرى (آسيا)",shortName:"Mohun Bagan"},{name:"Muangthong United",league:"🌏أندية أخرى (آسيا)",shortName:"Muangthong"},{name:"Nakhon Ratchasima FC",league:"🌏أندية أخرى (آسيا)",shortName:"Nakhon Ratch."},{name:"Nakhonpathom United FC",league:"🌏أندية أخرى (آسيا)",shortName:"Nakhonpathom"},{name:"Nongbua Pitchaya FC",league:"🌏أندية أخرى (آسيا)",shortName:"Nongbua"},{name:"Paxtakor FK",league:"🌏أندية أخرى (آسيا)",shortName:"Paxtakor"},{name:"Persepolis FC",league:"🌏أندية أخرى (آسيا)",shortName:"Persepolis"},{name:"Persib Bandung",league:"🌏أندية أخرى (آسيا)",shortName:"Persib"},{name:"Port FC",league:"🌏أندية أخرى (آسيا)",shortName:"Port FC"},{name:"PT Prachuap FC",league:"🌏أندية أخرى (آسيا)",shortName:"Prachuap"},{name:"Ratchaburi FC",league:"🌏أندية أخرى (آسيا)",shortName:"Ratchaburi"},{name:"Ravshan Kulob",league:"🌏أندية أخرى (آسيا)",shortName:"Ravshan"},{name:"Rayong FC",league:"🌏أندية أخرى (آسيا)",shortName:"Rayong"},{name:"Selangor FC",league:"🌏أندية أخرى (آسيا)",shortName:"Selangor"},{name:"Sepahan SC",league:"🌏أندية أخرى (آسيا)",shortName:"Sepahan"},{name:"Shabab Al Ahli Dubai FC",league:"🌏أندية أخرى (آسيا)",shortName:"Shabab Al Ahli"},{name:"Sharjah FC",league:"🌏أندية أخرى (آسيا)",shortName:"Sharjah"},{name:"Singha Chiangrai United",league:"🌏أندية أخرى (آسيا)",shortName:"Chiangrai"},{name:"Sukhothai FC",league:"🌏أندية أخرى (آسيا)",shortName:"Sukhothai"},{name:"Sydney FC",league:"🌏أندية أخرى (آسيا)",shortName:"Sydney FC"},{name:"Tampines Rovers FC",league:"🌏أندية أخرى (آسيا)",shortName:"Tampines"},{name:"Thep Xanh Nam Dinh FC",league:"🌏أندية أخرى (آسيا)",shortName:"Nam Dinh"},{name:"Tractor SC",league:"🌏أندية أخرى (آسيا)",shortName:"Tractor"},{name:"True Bangkok United FC",league:"🌏أندية أخرى (آسيا)",shortName:"Bangkok Utd"},{name:"Uthai Thani FC",league:"🌏أندية أخرى (آسيا)",shortName:"Uthai Thani"},{name:"Central Coast Mariners",league:"🌏أندية أخرى (آسيا)",shortName:"CC Mariners"}],sd=()=>[...Mh],zh=e=>{const t=[...e];for(let n=t.length-1;n>0;n--){const r=Math.floor(Math.random()*(n+1));[t[n],t[r]]=[t[r],t[n]]}return t},_h=["🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League","🇪🇸 La Liga","🇮🇹 Serie A","🇩🇪 Bundesliga","🇫🇷 Ligue 1"],$r=e=>!!e.league&&_h.includes(e.league),Bu=3,jh=3,Bh=(e,t)=>{const n=(e.strength||80)-(t.strength||80),r=4,a=Math.random()*3;let l=Math.max(1,Math.floor(a+r+n/8)),o=Math.max(1,Math.floor(a+r-n/8));return l===o&&(Math.random()>.5?l++:o++),[l,o]},Du=(e,t)=>{const n=t.reduce((l,o)=>l+o,0),r=Math.random()*n;let a=0;for(let l=0;l<e.length;l++)if(a+=t[l],r<=a)return e[l];return e[0]},Iu=e=>{const t=sd(),n=Ah(e);if(t.length<2)throw new Error("Not enough main clubs available. Please add more clubs to the mainClubs list.");if(n.length<8)throw new Error("Not enough regular clubs available. Please exclude fewer teams in settings.");const r=t.map(v=>$r(v)?Bu:1),a=Du(t,r),l=t.map(v=>{let C=1;return $r(v)&&(C=Bu),v.league&&a.league&&v.league===a.league&&(C*=jh),v.name===a.name&&(C=0),C}),o=Du(t,l);console.log("Selected clubs:",{player1:{name:a.name,league:a.league,isTopLeague:$r(a)},player2:{name:o.name,league:o.league,isTopLeague:$r(o)},sameLeague:a.league===o.league});const i=zh(n),u=Math.min(4,Math.floor(i.length/2)),s=i.slice(0,u),m=i.slice(u,u*2),[f,h]=Bh(a,o);return{player1Club:a,player2Club:o,player1TopTeams:s,player2TopTeams:m,score:{player1:f,player2:h},winner:f>h?1:2}};var gi={};Object.defineProperty(gi,"__esModule",{value:!0});gi.parse=Hh;gi.serialize=Wh;const Dh=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,Ih=/^[\u0021-\u003A\u003C-\u007E]*$/,Oh=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,Uh=/^[\u0020-\u003A\u003D-\u007E]*$/,$h=Object.prototype.toString,Vh=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function Hh(e,t){const n=new Vh,r=e.length;if(r<2)return n;const a=(t==null?void 0:t.decode)||Kh;let l=0;do{const o=e.indexOf("=",l);if(o===-1)break;const i=e.indexOf(";",l),u=i===-1?r:i;if(o>u){l=e.lastIndexOf(";",o-1)+1;continue}const s=Ou(e,l,o),m=Uu(e,o,s),f=e.slice(s,m);if(n[f]===void 0){let h=Ou(e,o+1,u),v=Uu(e,u,h);const C=a(e.slice(h,v));n[f]=C}l=u+1}while(l<r);return n}function Ou(e,t,n){do{const r=e.charCodeAt(t);if(r!==32&&r!==9)return t}while(++t<n);return n}function Uu(e,t,n){for(;t>n;){const r=e.charCodeAt(--t);if(r!==32&&r!==9)return t+1}return n}function Wh(e,t,n){const r=(n==null?void 0:n.encode)||encodeURIComponent;if(!Dh.test(e))throw new TypeError(`argument name is invalid: ${e}`);const a=r(t);if(!Ih.test(a))throw new TypeError(`argument val is invalid: ${t}`);let l=e+"="+a;if(!n)return l;if(n.maxAge!==void 0){if(!Number.isInteger(n.maxAge))throw new TypeError(`option maxAge is invalid: ${n.maxAge}`);l+="; Max-Age="+n.maxAge}if(n.domain){if(!Oh.test(n.domain))throw new TypeError(`option domain is invalid: ${n.domain}`);l+="; Domain="+n.domain}if(n.path){if(!Uh.test(n.path))throw new TypeError(`option path is invalid: ${n.path}`);l+="; Path="+n.path}if(n.expires){if(!Gh(n.expires)||!Number.isFinite(n.expires.valueOf()))throw new TypeError(`option expires is invalid: ${n.expires}`);l+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(l+="; HttpOnly"),n.secure&&(l+="; Secure"),n.partitioned&&(l+="; Partitioned"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():void 0){case"low":l+="; Priority=Low";break;case"medium":l+="; Priority=Medium";break;case"high":l+="; Priority=High";break;default:throw new TypeError(`option priority is invalid: ${n.priority}`)}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw new TypeError(`option sameSite is invalid: ${n.sameSite}`)}return l}function Kh(e){if(e.indexOf("%")===-1)return e;try{return decodeURIComponent(e)}catch{return e}}function Gh(e){return $h.call(e)==="[object Date]"}var $u="popstate";function Qh(e={}){function t(r,a){let{pathname:l,search:o,hash:i}=r.location;return vo("",{pathname:l,search:o,hash:i},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function n(r,a){return typeof a=="string"?a:mr(a)}return Jh(t,n,null,e)}function G(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Qe(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function bh(){return Math.random().toString(36).substring(2,10)}function Vu(e,t){return{usr:e.state,key:e.key,idx:t}}function vo(e,t,n=null,r){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof t=="string"?xn(t):t,state:n,key:t&&t.key||r||bh()}}function mr({pathname:e="/",search:t="",hash:n=""}){return t&&t!=="?"&&(e+=t.charAt(0)==="?"?t:"?"+t),n&&n!=="#"&&(e+=n.charAt(0)==="#"?n:"#"+n),e}function xn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function Jh(e,t,n,r={}){let{window:a=document.defaultView,v5Compat:l=!1}=r,o=a.history,i="POP",u=null,s=m();s==null&&(s=0,o.replaceState({...o.state,idx:s},""));function m(){return(o.state||{idx:null}).idx}function f(){i="POP";let L=m(),d=L==null?null:L-s;s=L,u&&u({action:i,location:N.location,delta:d})}function h(L,d){i="PUSH";let c=vo(N.location,L,d);s=m()+1;let g=Vu(c,s),S=N.createHref(c);try{o.pushState(g,"",S)}catch(w){if(w instanceof DOMException&&w.name==="DataCloneError")throw w;a.location.assign(S)}l&&u&&u({action:i,location:N.location,delta:1})}function v(L,d){i="REPLACE";let c=vo(N.location,L,d);s=m();let g=Vu(c,s),S=N.createHref(c);o.replaceState(g,"",S),l&&u&&u({action:i,location:N.location,delta:0})}function C(L){return Yh(L)}let N={get action(){return i},get location(){return e(a,o)},listen(L){if(u)throw new Error("A history only accepts one active listener");return a.addEventListener($u,f),u=L,()=>{a.removeEventListener($u,f),u=null}},createHref(L){return t(a,L)},createURL:C,encodeLocation(L){let d=C(L);return{pathname:d.pathname,search:d.search,hash:d.hash}},push:h,replace:v,go(L){return o.go(L)}};return N}function Yh(e,t=!1){let n="http://localhost";typeof window<"u"&&(n=window.location.origin!=="null"?window.location.origin:window.location.href),G(n,"No window.location.(origin|href) available to create URL");let r=typeof e=="string"?e:mr(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}function cd(e,t,n="/"){return Xh(e,t,n,!1)}function Xh(e,t,n,r){let a=typeof t=="string"?xn(t):t,l=ot(a.pathname||"/",n);if(l==null)return null;let o=dd(e);qh(o);let i=null;for(let u=0;i==null&&u<o.length;++u){let s=sg(l);i=ig(o[u],s,r)}return i}function dd(e,t=[],n=[],r=""){let a=(l,o,i)=>{let u={relativePath:i===void 0?l.path||"":i,caseSensitive:l.caseSensitive===!0,childrenIndex:o,route:l};u.relativePath.startsWith("/")&&(G(u.relativePath.startsWith(r),`Absolute route path "${u.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),u.relativePath=u.relativePath.slice(r.length));let s=tt([r,u.relativePath]),m=n.concat(u);l.children&&l.children.length>0&&(G(l.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${s}".`),dd(l.children,t,m,s)),!(l.path==null&&!l.index)&&t.push({path:s,score:lg(s,l.index),routesMeta:m})};return e.forEach((l,o)=>{var i;if(l.path===""||!((i=l.path)!=null&&i.includes("?")))a(l,o);else for(let u of md(l.path))a(l,o,u)}),t}function md(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(r.length===0)return a?[l,""]:[l];let o=md(r.join("/")),i=[];return i.push(...o.map(u=>u===""?l:[l,u].join("/"))),a&&i.push(...o),i.map(u=>e.startsWith("/")&&u===""?"/":u)}function qh(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:og(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}var Zh=/^:[\w-]+$/,eg=3,tg=2,ng=1,rg=10,ag=-2,Hu=e=>e==="*";function lg(e,t){let n=e.split("/"),r=n.length;return n.some(Hu)&&(r+=ag),t&&(r+=tg),n.filter(a=>!Hu(a)).reduce((a,l)=>a+(Zh.test(l)?eg:l===""?ng:rg),r)}function og(e,t){return e.length===t.length&&e.slice(0,-1).every((r,a)=>r===t[a])?e[e.length-1]-t[t.length-1]:0}function ig(e,t,n=!1){let{routesMeta:r}=e,a={},l="/",o=[];for(let i=0;i<r.length;++i){let u=r[i],s=i===r.length-1,m=l==="/"?t:t.slice(l.length)||"/",f=Fa({path:u.relativePath,caseSensitive:u.caseSensitive,end:s},m),h=u.route;if(!f&&s&&n&&!r[r.length-1].route.index&&(f=Fa({path:u.relativePath,caseSensitive:u.caseSensitive,end:!1},m)),!f)return null;Object.assign(a,f.params),o.push({params:a,pathname:tt([l,f.pathname]),pathnameBase:fg(tt([l,f.pathnameBase])),route:h}),f.pathnameBase!=="/"&&(l=tt([l,f.pathnameBase]))}return o}function Fa(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=ug(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],o=l.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:r.reduce((s,{paramName:m,isOptional:f},h)=>{if(m==="*"){let C=i[h]||"";o=l.slice(0,l.length-C.length).replace(/(.)\/+$/,"$1")}const v=i[h];return f&&!v?s[m]=void 0:s[m]=(v||"").replace(/%2F/g,"/"),s},{}),pathname:l,pathnameBase:o,pattern:e}}function ug(e,t=!1,n=!0){Qe(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,i,u)=>(r.push({paramName:i,isOptional:u!=null}),u?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function sg(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Qe(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function ot(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function cg(e,t="/"){let{pathname:n,search:r="",hash:a=""}=typeof e=="string"?xn(e):e;return{pathname:n?n.startsWith("/")?n:dg(n,t):t,search:hg(r),hash:gg(a)}}function dg(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?n.length>1&&n.pop():a!=="."&&n.push(a)}),n.length>1?n.join("/"):"/"}function Sl(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function mg(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function fd(e){let t=mg(e);return t.map((n,r)=>r===t.length-1?n.pathname:n.pathnameBase)}function hd(e,t,n,r=!1){let a;typeof e=="string"?a=xn(e):(a={...e},G(!a.pathname||!a.pathname.includes("?"),Sl("?","pathname","search",a)),G(!a.pathname||!a.pathname.includes("#"),Sl("#","pathname","hash",a)),G(!a.search||!a.search.includes("#"),Sl("#","search","hash",a)));let l=e===""||a.pathname==="",o=l?"/":a.pathname,i;if(o==null)i=n;else{let f=t.length-1;if(!r&&o.startsWith("..")){let h=o.split("/");for(;h[0]==="..";)h.shift(),f-=1;a.pathname=h.join("/")}i=f>=0?t[f]:"/"}let u=cg(a,i),s=o&&o!=="/"&&o.endsWith("/"),m=(l||o===".")&&n.endsWith("/");return!u.pathname.endsWith("/")&&(s||m)&&(u.pathname+="/"),u}var tt=e=>e.join("/").replace(/\/\/+/g,"/"),fg=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),hg=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,gg=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function pg(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var gd=["POST","PUT","PATCH","DELETE"];new Set(gd);var yg=["GET",...gd];new Set(yg);var kn=y.createContext(null);kn.displayName="DataRouter";var Ka=y.createContext(null);Ka.displayName="DataRouterState";var pd=y.createContext({isTransitioning:!1});pd.displayName="ViewTransition";var vg=y.createContext(new Map);vg.displayName="Fetchers";var Cg=y.createContext(null);Cg.displayName="Await";var be=y.createContext(null);be.displayName="Navigation";var Cr=y.createContext(null);Cr.displayName="Location";var ut=y.createContext({outlet:null,matches:[],isDataRoute:!1});ut.displayName="Route";var pi=y.createContext(null);pi.displayName="RouteError";function Ng(e,{relative:t}={}){G(Nr(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=y.useContext(be),{hash:a,pathname:l,search:o}=Sr(e,{relative:t}),i=l;return n!=="/"&&(i=l==="/"?n:tt([n,l])),r.createHref({pathname:i,search:o,hash:a})}function Nr(){return y.useContext(Cr)!=null}function Ht(){return G(Nr(),"useLocation() may be used only in the context of a <Router> component."),y.useContext(Cr).location}var yd="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function vd(e){y.useContext(be).static||y.useLayoutEffect(e)}function Sg(){let{isDataRoute:e}=y.useContext(ut);return e?_g():wg()}function wg(){G(Nr(),"useNavigate() may be used only in the context of a <Router> component.");let e=y.useContext(kn),{basename:t,navigator:n}=y.useContext(be),{matches:r}=y.useContext(ut),{pathname:a}=Ht(),l=JSON.stringify(fd(r)),o=y.useRef(!1);return vd(()=>{o.current=!0}),y.useCallback((u,s={})=>{if(Qe(o.current,yd),!o.current)return;if(typeof u=="number"){n.go(u);return}let m=hd(u,JSON.parse(l),a,s.relative==="path");e==null&&t!=="/"&&(m.pathname=m.pathname==="/"?t:tt([t,m.pathname])),(s.replace?n.replace:n.push)(m,s.state,s)},[t,n,l,a,e])}y.createContext(null);function Sr(e,{relative:t}={}){let{matches:n}=y.useContext(ut),{pathname:r}=Ht(),a=JSON.stringify(fd(n));return y.useMemo(()=>hd(e,JSON.parse(a),r,t==="path"),[e,a,r,t])}function xg(e,t){return Cd(e,t)}function Cd(e,t,n,r){var d;G(Nr(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a}=y.useContext(be),{matches:l}=y.useContext(ut),o=l[l.length-1],i=o?o.params:{},u=o?o.pathname:"/",s=o?o.pathnameBase:"/",m=o&&o.route;{let c=m&&m.path||"";Nd(u,!m||c.endsWith("*")||c.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${u}" (under <Route path="${c}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${c}"> to <Route path="${c==="/"?"*":`${c}/*`}">.`)}let f=Ht(),h;if(t){let c=typeof t=="string"?xn(t):t;G(s==="/"||((d=c.pathname)==null?void 0:d.startsWith(s)),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${s}" but pathname "${c.pathname}" was given in the \`location\` prop.`),h=c}else h=f;let v=h.pathname||"/",C=v;if(s!=="/"){let c=s.replace(/^\//,"").split("/");C="/"+v.replace(/^\//,"").split("/").slice(c.length).join("/")}let N=cd(e,{pathname:C});Qe(m||N!=null,`No routes matched location "${h.pathname}${h.search}${h.hash}" `),Qe(N==null||N[N.length-1].route.element!==void 0||N[N.length-1].route.Component!==void 0||N[N.length-1].route.lazy!==void 0,`Matched leaf route at location "${h.pathname}${h.search}${h.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let L=Fg(N&&N.map(c=>Object.assign({},c,{params:Object.assign({},i,c.params),pathname:tt([s,a.encodeLocation?a.encodeLocation(c.pathname).pathname:c.pathname]),pathnameBase:c.pathnameBase==="/"?s:tt([s,a.encodeLocation?a.encodeLocation(c.pathnameBase).pathname:c.pathnameBase])})),l,n,r);return t&&L?y.createElement(Cr.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...h},navigationType:"POP"}},L):L}function kg(){let e=zg(),t=pg(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:r},l={padding:"2px 4px",backgroundColor:r},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=y.createElement(y.Fragment,null,y.createElement("p",null,"💿 Hey developer 👋"),y.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",y.createElement("code",{style:l},"ErrorBoundary")," or"," ",y.createElement("code",{style:l},"errorElement")," prop on your route.")),y.createElement(y.Fragment,null,y.createElement("h2",null,"Unexpected Application Error!"),y.createElement("h3",{style:{fontStyle:"italic"}},t),n?y.createElement("pre",{style:a},n):null,o)}var Lg=y.createElement(kg,null),Eg=class extends y.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||t.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return this.state.error!==void 0?y.createElement(ut.Provider,{value:this.props.routeContext},y.createElement(pi.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Pg({routeContext:e,match:t,children:n}){let r=y.useContext(kn);return r&&r.static&&r.staticContext&&(t.route.errorElement||t.route.ErrorBoundary)&&(r.staticContext._deepestRenderedBoundaryId=t.route.id),y.createElement(ut.Provider,{value:e},n)}function Fg(e,t=[],n=null,r=null){if(e==null){if(!n)return null;if(n.errors)e=n.matches;else if(t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let a=e,l=n==null?void 0:n.errors;if(l!=null){let u=a.findIndex(s=>s.route.id&&(l==null?void 0:l[s.route.id])!==void 0);G(u>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(l).join(",")}`),a=a.slice(0,Math.min(a.length,u+1))}let o=!1,i=-1;if(n)for(let u=0;u<a.length;u++){let s=a[u];if((s.route.HydrateFallback||s.route.hydrateFallbackElement)&&(i=u),s.route.id){let{loaderData:m,errors:f}=n,h=s.route.loader&&!m.hasOwnProperty(s.route.id)&&(!f||f[s.route.id]===void 0);if(s.route.lazy||h){o=!0,i>=0?a=a.slice(0,i+1):a=[a[0]];break}}}return a.reduceRight((u,s,m)=>{let f,h=!1,v=null,C=null;n&&(f=l&&s.route.id?l[s.route.id]:void 0,v=s.route.errorElement||Lg,o&&(i<0&&m===0?(Nd("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),h=!0,C=null):i===m&&(h=!0,C=s.route.hydrateFallbackElement||null)));let N=t.concat(a.slice(0,m+1)),L=()=>{let d;return f?d=v:h?d=C:s.route.Component?d=y.createElement(s.route.Component,null):s.route.element?d=s.route.element:d=u,y.createElement(Pg,{match:s,routeContext:{outlet:u,matches:N,isDataRoute:n!=null},children:d})};return n&&(s.route.ErrorBoundary||s.route.errorElement||m===0)?y.createElement(Eg,{location:n.location,revalidation:n.revalidation,component:v,error:f,children:L(),routeContext:{outlet:null,matches:N,isDataRoute:!0}}):L()},null)}function yi(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Ag(e){let t=y.useContext(kn);return G(t,yi(e)),t}function Rg(e){let t=y.useContext(Ka);return G(t,yi(e)),t}function Tg(e){let t=y.useContext(ut);return G(t,yi(e)),t}function vi(e){let t=Tg(e),n=t.matches[t.matches.length-1];return G(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function Mg(){return vi("useRouteId")}function zg(){var r;let e=y.useContext(pi),t=Rg("useRouteError"),n=vi("useRouteError");return e!==void 0?e:(r=t.errors)==null?void 0:r[n]}function _g(){let{router:e}=Ag("useNavigate"),t=vi("useNavigate"),n=y.useRef(!1);return vd(()=>{n.current=!0}),y.useCallback(async(a,l={})=>{Qe(n.current,yd),n.current&&(typeof a=="number"?e.navigate(a):await e.navigate(a,{fromRouteId:t,...l}))},[e,t])}var Wu={};function Nd(e,t,n){!t&&!Wu[e]&&(Wu[e]=!0,Qe(!1,n))}y.memo(jg);function jg({routes:e,future:t,state:n}){return Cd(e,void 0,n,t)}function Co(e){G(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Bg({basename:e="/",children:t=null,location:n,navigationType:r="POP",navigator:a,static:l=!1}){G(!Nr(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let o=e.replace(/^\/*/,"/"),i=y.useMemo(()=>({basename:o,navigator:a,static:l,future:{}}),[o,a,l]);typeof n=="string"&&(n=xn(n));let{pathname:u="/",search:s="",hash:m="",state:f=null,key:h="default"}=n,v=y.useMemo(()=>{let C=ot(u,o);return C==null?null:{location:{pathname:C,search:s,hash:m,state:f,key:h},navigationType:r}},[o,u,s,m,f,h,r]);return Qe(v!=null,`<Router basename="${o}"> is not able to match the URL "${u}${s}${m}" because it does not start with the basename, so the <Router> won't render anything.`),v==null?null:y.createElement(be.Provider,{value:i},y.createElement(Cr.Provider,{children:t,value:v}))}function Dg({children:e,location:t}){return xg(No(e),t)}function No(e,t=[]){let n=[];return y.Children.forEach(e,(r,a)=>{if(!y.isValidElement(r))return;let l=[...t,a];if(r.type===y.Fragment){n.push.apply(n,No(r.props.children,l));return}G(r.type===Co,`[${typeof r.type=="string"?r.type:r.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),G(!r.props.index||!r.props.children,"An index route cannot have child routes.");let o={id:r.props.id||l.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,hydrateFallbackElement:r.props.hydrateFallbackElement,HydrateFallback:r.props.HydrateFallback,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.hasErrorBoundary===!0||r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=No(r.props.children,l)),n.push(o)}),n}var na="get",ra="application/x-www-form-urlencoded";function Ga(e){return e!=null&&typeof e.tagName=="string"}function Ig(e){return Ga(e)&&e.tagName.toLowerCase()==="button"}function Og(e){return Ga(e)&&e.tagName.toLowerCase()==="form"}function Ug(e){return Ga(e)&&e.tagName.toLowerCase()==="input"}function $g(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Vg(e,t){return e.button===0&&(!t||t==="_self")&&!$g(e)}var Vr=null;function Hg(){if(Vr===null)try{new FormData(document.createElement("form"),0),Vr=!1}catch{Vr=!0}return Vr}var Wg=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function wl(e){return e!=null&&!Wg.has(e)?(Qe(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${ra}"`),null):e}function Kg(e,t){let n,r,a,l,o;if(Og(e)){let i=e.getAttribute("action");r=i?ot(i,t):null,n=e.getAttribute("method")||na,a=wl(e.getAttribute("enctype"))||ra,l=new FormData(e)}else if(Ig(e)||Ug(e)&&(e.type==="submit"||e.type==="image")){let i=e.form;if(i==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let u=e.getAttribute("formaction")||i.getAttribute("action");if(r=u?ot(u,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||na,a=wl(e.getAttribute("formenctype"))||wl(i.getAttribute("enctype"))||ra,l=new FormData(i,e),!Hg()){let{name:s,type:m,value:f}=e;if(m==="image"){let h=s?`${s}.`:"";l.append(`${h}x`,"0"),l.append(`${h}y`,"0")}else s&&l.append(s,f)}}else{if(Ga(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=na,r=null,a=ra,o=e}return l&&a==="text/plain"&&(o=l,l=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:l,body:o}}function Ci(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}async function Gg(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Qg(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function bg(e,t,n){let r=await Promise.all(e.map(async a=>{let l=t.routes[a.route.id];if(l){let o=await Gg(l,n);return o.links?o.links():[]}return[]}));return qg(r.flat(1).filter(Qg).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function Ku(e,t,n,r,a,l){let o=(u,s)=>n[s]?u.route.id!==n[s].route.id:!0,i=(u,s)=>{var m;return n[s].pathname!==u.pathname||((m=n[s].route.path)==null?void 0:m.endsWith("*"))&&n[s].params["*"]!==u.params["*"]};return l==="assets"?t.filter((u,s)=>o(u,s)||i(u,s)):l==="data"?t.filter((u,s)=>{var f;let m=r.routes[u.route.id];if(!m||!m.hasLoader)return!1;if(o(u,s)||i(u,s))return!0;if(u.route.shouldRevalidate){let h=u.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:((f=n[0])==null?void 0:f.params)||{},nextUrl:new URL(e,window.origin),nextParams:u.params,defaultShouldRevalidate:!0});if(typeof h=="boolean")return h}return!0}):[]}function Jg(e,t,{includeHydrateFallback:n}={}){return Yg(e.map(r=>{let a=t.routes[r.route.id];if(!a)return[];let l=[a.module];return a.clientActionModule&&(l=l.concat(a.clientActionModule)),a.clientLoaderModule&&(l=l.concat(a.clientLoaderModule)),n&&a.hydrateFallbackModule&&(l=l.concat(a.hydrateFallbackModule)),a.imports&&(l=l.concat(a.imports)),l}).flat(1))}function Yg(e){return[...new Set(e)]}function Xg(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}function qg(e,t){let n=new Set;return new Set(t),e.reduce((r,a)=>{let l=JSON.stringify(Xg(a));return n.has(l)||(n.add(l),r.push({key:l,link:a})),r},[])}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var Zg=new Set([100,101,204,205]);function ep(e,t){let n=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return n.pathname==="/"?n.pathname="_root.data":t&&ot(n.pathname,t)==="/"?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}function Sd(){let e=y.useContext(kn);return Ci(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function tp(){let e=y.useContext(Ka);return Ci(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Ni=y.createContext(void 0);Ni.displayName="FrameworkContext";function wd(){let e=y.useContext(Ni);return Ci(e,"You must render this element inside a <HydratedRouter> element"),e}function np(e,t){let n=y.useContext(Ni),[r,a]=y.useState(!1),[l,o]=y.useState(!1),{onFocus:i,onBlur:u,onMouseEnter:s,onMouseLeave:m,onTouchStart:f}=t,h=y.useRef(null);y.useEffect(()=>{if(e==="render"&&o(!0),e==="viewport"){let N=d=>{d.forEach(c=>{o(c.isIntersecting)})},L=new IntersectionObserver(N,{threshold:.5});return h.current&&L.observe(h.current),()=>{L.disconnect()}}},[e]),y.useEffect(()=>{if(r){let N=setTimeout(()=>{o(!0)},100);return()=>{clearTimeout(N)}}},[r]);let v=()=>{a(!0)},C=()=>{a(!1),o(!1)};return n?e!=="intent"?[l,h,{}]:[l,h,{onFocus:jn(i,v),onBlur:jn(u,C),onMouseEnter:jn(s,v),onMouseLeave:jn(m,C),onTouchStart:jn(f,v)}]:[!1,h,{}]}function jn(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function rp({page:e,...t}){let{router:n}=Sd(),r=y.useMemo(()=>cd(n.routes,e,n.basename),[n.routes,e,n.basename]);return r?y.createElement(lp,{page:e,matches:r,...t}):null}function ap(e){let{manifest:t,routeModules:n}=wd(),[r,a]=y.useState([]);return y.useEffect(()=>{let l=!1;return bg(e,t,n).then(o=>{l||a(o)}),()=>{l=!0}},[e,t,n]),r}function lp({page:e,matches:t,...n}){let r=Ht(),{manifest:a,routeModules:l}=wd(),{basename:o}=Sd(),{loaderData:i,matches:u}=tp(),s=y.useMemo(()=>Ku(e,t,u,a,r,"data"),[e,t,u,a,r]),m=y.useMemo(()=>Ku(e,t,u,a,r,"assets"),[e,t,u,a,r]),f=y.useMemo(()=>{if(e===r.pathname+r.search+r.hash)return[];let C=new Set,N=!1;if(t.forEach(d=>{var g;let c=a.routes[d.route.id];!c||!c.hasLoader||(!s.some(S=>S.route.id===d.route.id)&&d.route.id in i&&((g=l[d.route.id])!=null&&g.shouldRevalidate)||c.hasClientLoader?N=!0:C.add(d.route.id))}),C.size===0)return[];let L=ep(e,o);return N&&C.size>0&&L.searchParams.set("_routes",t.filter(d=>C.has(d.route.id)).map(d=>d.route.id).join(",")),[L.pathname+L.search]},[o,i,r,a,s,t,e,l]),h=y.useMemo(()=>Jg(m,a),[m,a]),v=ap(m);return y.createElement(y.Fragment,null,f.map(C=>y.createElement("link",{key:C,rel:"prefetch",as:"fetch",href:C,...n})),h.map(C=>y.createElement("link",{key:C,rel:"modulepreload",href:C,...n})),v.map(({key:C,link:N})=>y.createElement("link",{key:C,...N})))}function op(...e){return t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})}}var xd=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{xd&&(window.__reactRouterVersion="7.6.1")}catch{}function ip({basename:e,children:t,window:n}){let r=y.useRef();r.current==null&&(r.current=Qh({window:n,v5Compat:!0}));let a=r.current,[l,o]=y.useState({action:a.action,location:a.location}),i=y.useCallback(u=>{y.startTransition(()=>o(u))},[o]);return y.useLayoutEffect(()=>a.listen(i),[a,i]),y.createElement(Bg,{basename:e,children:t,location:l.location,navigationType:l.action,navigator:a})}var kd=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,fr=y.forwardRef(function({onClick:t,discover:n="render",prefetch:r="none",relative:a,reloadDocument:l,replace:o,state:i,target:u,to:s,preventScrollReset:m,viewTransition:f,...h},v){let{basename:C}=y.useContext(be),N=typeof s=="string"&&kd.test(s),L,d=!1;if(typeof s=="string"&&N&&(L=s,xd))try{let T=new URL(window.location.href),R=s.startsWith("//")?new URL(T.protocol+s):new URL(s),q=ot(R.pathname,C);R.origin===T.origin&&q!=null?s=q+R.search+R.hash:d=!0}catch{Qe(!1,`<Link to="${s}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let c=Ng(s,{relative:a}),[g,S,w]=np(r,h),k=dp(s,{replace:o,state:i,target:u,preventScrollReset:m,relative:a,viewTransition:f});function E(T){t&&t(T),T.defaultPrevented||k(T)}let P=y.createElement("a",{...h,...w,href:L||c,onClick:d||l?t:E,ref:op(v,S),target:u,"data-discover":!N&&n==="render"?"true":void 0});return g&&!N?y.createElement(y.Fragment,null,P,y.createElement(rp,{page:c})):P});fr.displayName="Link";var up=y.forwardRef(function({"aria-current":t="page",caseSensitive:n=!1,className:r="",end:a=!1,style:l,to:o,viewTransition:i,children:u,...s},m){let f=Sr(o,{relative:s.relative}),h=Ht(),v=y.useContext(Ka),{navigator:C,basename:N}=y.useContext(be),L=v!=null&&pp(f)&&i===!0,d=C.encodeLocation?C.encodeLocation(f).pathname:f.pathname,c=h.pathname,g=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;n||(c=c.toLowerCase(),g=g?g.toLowerCase():null,d=d.toLowerCase()),g&&N&&(g=ot(g,N)||g);const S=d!=="/"&&d.endsWith("/")?d.length-1:d.length;let w=c===d||!a&&c.startsWith(d)&&c.charAt(S)==="/",k=g!=null&&(g===d||!a&&g.startsWith(d)&&g.charAt(d.length)==="/"),E={isActive:w,isPending:k,isTransitioning:L},P=w?t:void 0,T;typeof r=="function"?T=r(E):T=[r,w?"active":null,k?"pending":null,L?"transitioning":null].filter(Boolean).join(" ");let R=typeof l=="function"?l(E):l;return y.createElement(fr,{...s,"aria-current":P,className:T,ref:m,style:R,to:o,viewTransition:i},typeof u=="function"?u(E):u)});up.displayName="NavLink";var sp=y.forwardRef(({discover:e="render",fetcherKey:t,navigate:n,reloadDocument:r,replace:a,state:l,method:o=na,action:i,onSubmit:u,relative:s,preventScrollReset:m,viewTransition:f,...h},v)=>{let C=hp(),N=gp(i,{relative:s}),L=o.toLowerCase()==="get"?"get":"post",d=typeof i=="string"&&kd.test(i),c=g=>{if(u&&u(g),g.defaultPrevented)return;g.preventDefault();let S=g.nativeEvent.submitter,w=(S==null?void 0:S.getAttribute("formmethod"))||o;C(S||g.currentTarget,{fetcherKey:t,method:w,navigate:n,replace:a,state:l,relative:s,preventScrollReset:m,viewTransition:f})};return y.createElement("form",{ref:v,method:L,action:N,onSubmit:r?u:c,...h,"data-discover":!d&&e==="render"?"true":void 0})});sp.displayName="Form";function cp(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Ld(e){let t=y.useContext(kn);return G(t,cp(e)),t}function dp(e,{target:t,replace:n,state:r,preventScrollReset:a,relative:l,viewTransition:o}={}){let i=Sg(),u=Ht(),s=Sr(e,{relative:l});return y.useCallback(m=>{if(Vg(m,t)){m.preventDefault();let f=n!==void 0?n:mr(u)===mr(s);i(e,{replace:f,state:r,preventScrollReset:a,relative:l,viewTransition:o})}},[u,i,s,n,r,t,e,a,l,o])}var mp=0,fp=()=>`__${String(++mp)}__`;function hp(){let{router:e}=Ld("useSubmit"),{basename:t}=y.useContext(be),n=Mg();return y.useCallback(async(r,a={})=>{let{action:l,method:o,encType:i,formData:u,body:s}=Kg(r,t);if(a.navigate===!1){let m=a.fetcherKey||fp();await e.fetch(m,n,a.action||l,{preventScrollReset:a.preventScrollReset,formData:u,body:s,formMethod:a.method||o,formEncType:a.encType||i,flushSync:a.flushSync})}else await e.navigate(a.action||l,{preventScrollReset:a.preventScrollReset,formData:u,body:s,formMethod:a.method||o,formEncType:a.encType||i,replace:a.replace,state:a.state,fromRouteId:n,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,n])}function gp(e,{relative:t}={}){let{basename:n}=y.useContext(be),r=y.useContext(ut);G(r,"useFormAction must be used inside a RouteContext");let[a]=r.matches.slice(-1),l={...Sr(e||".",{relative:t})},o=Ht();if(e==null){l.search=o.search;let i=new URLSearchParams(l.search),u=i.getAll("index");if(u.some(m=>m==="")){i.delete("index"),u.filter(f=>f).forEach(f=>i.append("index",f));let m=i.toString();l.search=m?`?${m}`:""}}return(!e||e===".")&&a.route.index&&(l.search=l.search?l.search.replace(/^\?/,"?index&"):"?index"),n!=="/"&&(l.pathname=l.pathname==="/"?n:tt([n,l.pathname])),mr(l)}function pp(e,t={}){let n=y.useContext(pd);G(n!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=Ld("useViewTransitionState"),a=Sr(e,{relative:t.relative});if(!n.isTransitioning)return!1;let l=ot(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=ot(n.nextLocation.pathname,r)||n.nextLocation.pathname;return Fa(a.pathname,o)!=null||Fa(a.pathname,l)!=null}[...Zg];function xl(e,t){const[n,r]=y.useState(()=>{try{const l=window.localStorage.getItem(e);return l?JSON.parse(l):t}catch(l){return console.error(`Error reading localStorage key "${e}":`,l),t}});return[n,l=>{try{const o=l instanceof Function?l(n):l;r(o),window.localStorage.setItem(e,JSON.stringify(o))}catch(o){console.error(`Error setting localStorage key "${e}":`,o)}}]}const Hr={GEMINI_API_KEY:"AIzaSyCwaY5RqPR9llMopkMH9xc1pM2EwlSgBKM",API_URL:"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent",QUESTIONS_COUNT:10},kl={BACKGROUND:"selectedBackground",QUIZ_BACKGROUND_TOGGLE:"quizUseBackground",QUIZ_LANGUAGE:"quizLanguage"};function yp(){const[e,t]=y.useState(!1),[n,r]=y.useState("");return{loading:e,error:n,generateQuestions:async(o,i)=>{if(!o.trim())throw new Error("Category cannot be empty.");t(!0),r("");const u=i==="ar"?`أنشئ 10 أسئلة اختبار عن كرة القدم حول ${o} بصيغة JSON. كل سؤال يجب أن يحتوي على "questionText" و مصفوفة "answerOptions". كل خيار إجابة يجب أن يحتوي على "answerText" و "isCorrect" boolean. تأكد من وجود إجابة صحيحة واحدة فقط لكل سؤال. اكتب الأسئلة والإجابات باللغة العربية باللهجة المصرية.`:`Generate 10 football trivia questions about ${o} in JSON format. Each question should have a "questionText" and an "answerOptions" array. Each answer option should have "answerText" and a "isCorrect" boolean. Ensure there is exactly one correct answer per question.`,s=[];s.push({role:"user",parts:[{text:u}]});const m={contents:s,generationConfig:{responseMimeType:"application/json",responseSchema:{type:"ARRAY",items:{type:"OBJECT",properties:{questionText:{type:"STRING"},answerOptions:{type:"ARRAY",items:{type:"OBJECT",properties:{answerText:{type:"STRING"},isCorrect:{type:"BOOLEAN"}},required:["answerText","isCorrect"]}}},required:["questionText","answerOptions"]}}}},f=`${Hr.API_URL}?key=${Hr.GEMINI_API_KEY}`;try{const v=await(await fetch(f,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)})).json();if(v.candidates&&v.candidates.length>0&&v.candidates[0].content&&v.candidates[0].content.parts&&v.candidates[0].content.parts.length>0){const C=v.candidates[0].content.parts[0].text;try{const N=JSON.parse(C);if(Array.isArray(N)&&N.length>0)return N;throw new Error("Received invalid question format from API. Please try again.")}catch{throw new Error("Failed to parse questions from API. Please try again.")}}else throw new Error("Could not generate questions. Please try again.")}catch{const v="Failed to fetch questions. Network error or API issue.";throw r(v),new Error(v)}finally{t(!1)}},getExplanation:async(o,i,u)=>{t(!0),r("");const s=u==="ar"?`اشرح الإجابة الصحيحة لسؤال كرة القدم التالي: "${o}". الإجابة الصحيحة هي "${i}". قدم شرحاً مختصراً باللغة العربية باللهجة المصرية.`:`Explain the correct answer to the following football trivia question: "${o}". The correct answer is "${i}". Provide a concise explanation.`,m=[];m.push({role:"user",parts:[{text:s}]});const f={contents:m},h=`${Hr.API_URL}?key=${Hr.GEMINI_API_KEY}`;try{const C=await(await fetch(h,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(f)})).json();if(C.candidates&&C.candidates.length>0&&C.candidates[0].content&&C.candidates[0].content.parts&&C.candidates[0].content.parts.length>0&&C.candidates[0].content.parts[0].text)return C.candidates[0].content.parts[0].text;throw new Error("Could not get an explanation. Please try again.")}catch{const v="Failed to fetch explanation. Network error or API issue.";throw r(v),new Error(v)}finally{t(!1)}}}}const vp={en:["World Cup","Champions League","Player Trivia","Football Rules","Club Football","International Football"],ar:["كأس العالم","دوري أبطال أوروبا","معلومات اللاعبين","قوانين كرة القدم","كرة القدم للأندية","كرة القدم الدولية"]},Cp=[{code:"en",name:"English",flag:"🇬🇧"},{code:"ar",name:"العربية",flag:"🇪🇬"}],Np={en:{home:"← Home",testKnowledge:"Test your football knowledge!",chooseMode:"Choose Mode",singlePlayer:"Single Player",multiPlayer:"Multi-Player",comingSoon:"Coming Soon 🚧",back:"Back",backToSelectMode:"Back to Select Mode",chooseCategory:"Choose a Football Category",customCategory:"Or Enter Your Own Category",customCategoryPlaceholder:"e.g., 'Premier League History'",startCustomQuiz:"Start Custom Quiz",loadingQuestions:"Loading Questions...",loadingQuestionsDesc:"Please wait while we fetch your trivia questions.",quizCompleted:"Quiz Completed!",youScored:"You scored",outOf:"out of",playAgain:"Play Again / Choose Category",backToHome:"Back to Home",question:"Question",cancelQuiz:"Cancel Quiz",correct:"Correct!",incorrect:"Incorrect!",explainAnswer:"✨ Explain Answer",loadingExplanation:"Loading Explanation...",explanation:"Explanation:",nextQuestion:"Next Question",finishQuiz:"Finish Quiz",switchToArabic:"Switch to Arabic",switchToEnglish:"Switch to English",disableBackground:"Disable background",enableBackground:"Enable background"},ar:{home:"→ الرئيسية",testKnowledge:"اختبر معرفتك بكرة القدم!",chooseMode:"اختار وضع اللعب",singlePlayer:"لعب فردى",multiPlayer:"تحدى 1v1",comingSoon:"قريباً 🚧",back:"رجوع",backToSelectMode:"العودة لاختيار وضع اللعب",chooseCategory:"إختر موضوع الأسئلة",customCategory:"أو أدخل الموضوع بنفسك ",customCategoryPlaceholder:'مثال: "تاريخ الدوري الإنجليزي"',startCustomQuiz:"ابدأ الاختبار 💪",loadingQuestions:"جارى تحميل الأسئلة...",loadingQuestionsDesc:"يرجى الانتظار بينما نجلب أسئلة الاختبار.",quizCompleted:"تم إكمال الاختبار!",youScored:"لقد حصلت على",outOf:"من أصل",playAgain:"العب مرة أخرى / اختر موضوع",backToHome:"العودة للرئيسية",question:"السؤال",cancelQuiz:"إلغاء الاختبار",correct:"صحيح!",incorrect:"خطأ!",explainAnswer:"✨ إشرحلى الإجابة",loadingExplanation:"جاري تحميل الشرح...",explanation:"الشرح:",nextQuestion:"السؤال التالى",finishQuiz:"إنهاء الاختبار",switchToArabic:"التبديل للعربية",switchToEnglish:"التبديل للإنجليزية",disableBackground:"إيقاف الخلفية",enableBackground:"تفعيل الخلفية"}},Sp=(e,t)=>e&&t?{backgroundImage:`linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(${e.url})`,backgroundSize:"cover",backgroundPosition:"center"}:{},wp=(e,t)=>e&&t?"min-h-screen flex items-center justify-center p-4 font-inter transition-all duration-500":"min-h-screen bg-gradient-to-br from-green-500 to-blue-600 flex items-center justify-center p-4 font-inter",xp=()=>{var Si,wi,xi;const[e]=xl(kl.BACKGROUND,null),[t,n]=xl(kl.QUIZ_BACKGROUND_TOGGLE,!0),[r,a]=xl(kl.QUIZ_LANGUAGE,"en"),[l,o]=y.useState(0),[i,u]=y.useState(0),[s,m]=y.useState(!1),[f,h]=y.useState(null),[v,C]=y.useState(""),[N,L]=y.useState(!1),[d,c]=y.useState(""),[g,S]=y.useState(null),[w,k]=y.useState([]),[E,P]=y.useState(""),[T,R]=y.useState(null),{loading:q,error:Z,generateQuestions:de,getExplanation:wr}=yp(),[xr,Wt]=y.useState(!1),_=I=>Np[r][I],F=()=>{n(!t)},M=()=>{a(r==="en"?"ar":"en")},z=I=>{N||(h(I),L(!0),I?(u(i+1),C(_("correct"))):C(_("incorrect")))},H=()=>{const I=l+1;I<w.length?(o(I),h(null),C(""),L(!1),c("")):m(!0)},J=()=>{o(0),u(0),m(!1),h(null),C(""),L(!1),c(""),k([]),S(null),P(""),R(null)},Kt=async()=>{var ki;Wt(!0),c("");const I=w[l],En=(ki=I.answerOptions.find(Qa=>Qa.isCorrect))==null?void 0:ki.answerText;try{const Qa=await wr(I.questionText,En||"",r);c(Qa)}catch{c("Failed to fetch explanation. Please try again.")}finally{Wt(!1)}},$e=async I=>{S(I),o(0),u(0),m(!1),h(null),C(""),L(!1),c("");try{const En=await de(I,r);k(En)}catch{console.error("Failed to fetch questions")}},Ln=()=>{$e(E)},Je=Sp(e,t),Gt=wp(e,t);return p.jsxs("div",{className:Gt,style:Je,dir:r==="ar"?"rtl":"ltr",children:[p.jsx(fr,{to:"/",className:`absolute top-4 ${r==="ar"?"right-4":"left-4"} bg-gradient-to-r from-yellow-400 to-yellow-200 text-black font-bold py-2 px-5 rounded-full shadow-md hover:scale-105 transition-all duration-300 border-2 border-yellow-500`,children:_("home")}),p.jsxs("button",{onClick:M,className:`absolute top-4 ${r==="ar"?"left-16":"right-16"} bg-gradient-to-r from-purple-400 to-purple-600 text-white p-2 rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 z-10 flex items-center gap-1`,title:_(r==="en"?"switchToArabic":"switchToEnglish"),children:[p.jsx(oh,{size:16}),p.jsx("span",{className:"text-sm font-medium",children:(Si=Cp.find(I=>I.code===r))==null?void 0:Si.flag})]}),e&&p.jsx("button",{onClick:F,className:`absolute top-4 ${r==="ar"?"left-4":"right-4"} bg-gradient-to-r from-blue-400 to-blue-600 text-white p-2 rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 z-10`,title:_(t?"disableBackground":"enableBackground"),children:p.jsx(od,{size:20})}),p.jsxs("div",{className:"bg-white rounded-3xl shadow-2xl p-8 w-full max-w-xl border-4 border-yellow-300 relative",children:[p.jsxs("div",{className:"text-center mb-8",children:[p.jsx("h2",{className:"text-4xl md:text-5xl font-extrabold gradient-red-blue inline-block text-transparent bg-clip-text mb-2",children:"Football Quiz"}),p.jsx("p",{className:"text-lg text-gray-600 font-medium",children:_("testKnowledge")})]}),!T&&p.jsxs("div",{className:"text-center mb-8",children:[p.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:_("chooseMode")}),p.jsxs("div",{className:"flex flex-col gap-4 justify-center items-center",children:[p.jsxs("button",{onClick:()=>R("Single Player"),className:"py-3 px-6 text-lg font-bold rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 focus:outline-none flex items-center justify-center gradient-yellow",style:{minWidth:"200px"},children:[p.jsx(vh,{size:20,className:r==="ar"?"ml-2":"mr-2"}),_("singlePlayer")]}),p.jsxs("button",{onClick:()=>R("Multi-Player"),className:"py-3 px-6 text-lg font-bold rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 focus:outline-none flex items-center justify-center bg-gray-200 text-gray-800 hover:bg-gray-300",style:{minWidth:"200px"},children:[p.jsx(Nh,{size:20,className:r==="ar"?"ml-2":"mr-2"}),_("multiPlayer")]})]})]}),T==="Multi-Player"&&p.jsxs("div",{className:"text-center mt-12",children:[p.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:_("multiPlayer")}),p.jsx("p",{className:"text-xl text-gray-700 mb-6",children:_("comingSoon")}),p.jsx("button",{onClick:()=>R(null),className:"bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-xl transition duration-300 ease-in-out shadow-lg",children:_("back")})]}),T==="Single Player"&&(!g||w.length===0&&!q&&!Z?p.jsxs("div",{className:"text-center",children:[p.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-6",children:_("chooseCategory")}),p.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6",children:vp[r].map(I=>p.jsx("button",{onClick:()=>$e(I),className:"font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg text-yellow-100 border-yellow-300",style:{background:"linear-gradient(110deg, var(--barça-blue) 50%, var(--barça-red) 50%)"},children:I},I))}),p.jsxs("div",{className:"mt-8",children:[p.jsx("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:_("customCategory")}),p.jsx("input",{type:"text",value:E,onChange:I=>P(I.target.value),placeholder:_("customCategoryPlaceholder"),className:"w-full p-3 border-2 border-gray-300 rounded-xl focus:outline-none focus:border-blue-500 mb-4 text-gray-800",dir:r==="ar"?"rtl":"ltr"}),p.jsx("button",{onClick:Ln,className:"w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg border-2",disabled:!E.trim(),children:_("startCustomQuiz")})]}),Z&&p.jsx("p",{className:"text-red-500 mt-4 text-sm",children:Z}),p.jsx("button",{onClick:()=>R(null),className:"w-full mt-4 text-gray-700 font-bold py-2 px-6 rounded-xl transition duration-300",children:_("backToSelectMode")})]}):q?p.jsxs("div",{className:"text-center",children:[p.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:_("loadingQuestions")}),p.jsx("p",{className:"text-gray-600",children:_("loadingQuestionsDesc")}),p.jsx("div",{className:"flex justify-center items-center mt-6",children:p.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})]}):s?p.jsxs("div",{className:"text-center",children:[p.jsx("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:_("quizCompleted")}),p.jsxs("p",{className:"text-xl text-gray-700 mb-6",children:[_("youScored")," ",p.jsx("span",{className:"font-bold text-purple-700",children:i})," ",_("outOf")," ",p.jsx("span",{className:"font-bold text-purple-700",children:w.length})]}),p.jsx("button",{onClick:J,className:"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg mb-4",children:_("playAgain")}),p.jsx(fr,{to:"/",className:"w-full block bg-yellow-400 hover:bg-yellow-500 text-black font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg border-2 border-yellow-500",children:_("backToHome")})]}):p.jsxs("div",{children:[p.jsxs("div",{className:`flex justify-between items-center mb-6 ${r==="ar"?"flex-row-reverse":""}`,children:[p.jsxs("div",{className:"text-lg font-semibold text-gray-600",children:[_("question")," ",p.jsx("span",{className:"font-bold text-blue-700",children:l+1})," / ",w.length]}),p.jsx("button",{onClick:J,className:"bg-red-500 hover:bg-red-600 text-white text-sm font-bold py-2 px-4 rounded-xl transition duration-300 ease-in-out shadow-md",children:_("cancelQuiz")})]}),p.jsx("div",{className:"text-2xl font-bold text-gray-800 mb-6 bg-gradient-to-r from-blue-100 to-purple-100 rounded-lg p-4 shadow-inner",children:(wi=w[l])==null?void 0:wi.questionText}),p.jsx("div",{className:"space-y-4 mb-6",children:(xi=w[l])==null?void 0:xi.answerOptions.map((I,En)=>p.jsx("button",{onClick:()=>z(I.isCorrect),className:`
                      w-full ${r==="ar"?"text-right":"text-left"} py-3 px-4 rounded-xl border-2
                      transition duration-200 ease-in-out font-semibold text-lg
                      ${f===null?"bg-gray-100 hover:bg-gray-200 border-gray-300 text-gray-800":I.isCorrect?"bg-green-100 border-green-500 text-green-800":f!==null&&!I.isCorrect&&f===!1&&f!==void 0?"bg-red-100 border-red-500 text-red-800":"bg-gray-100 border-gray-300 text-gray-800 opacity-70 cursor-not-allowed"}
                      ${N?"cursor-not-allowed":"cursor-pointer"}
                    `,disabled:N,children:I.answerText},En))}),v&&p.jsx("div",{className:`text-center text-lg font-bold mb-4
                    ${f?"text-green-600":"text-red-600"}
                  `,children:v}),f!==null&&p.jsxs("div",{className:"mt-4",children:[p.jsx("button",{onClick:Kt,className:"w-full bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg mb-4",disabled:xr,children:_(xr?"loadingExplanation":"explainAnswer")}),d&&p.jsxs("div",{className:"bg-gray-50 p-4 rounded-lg text-gray-700 text-base border border-gray-200",dir:r==="ar"?"rtl":"ltr",children:[p.jsx("h3",{className:"font-semibold text-base mb-2",children:_("explanation")}),p.jsx("p",{children:d})]})]}),p.jsx("button",{onClick:H,className:`w-full font-bold py-3 px-6 rounded-xl transition duration-300 ease-in-out transform hover:scale-105 shadow-lg mt-6
                  ${f!==null?"bg-purple-600 hover:bg-purple-700 text-white":"bg-gray-300 text-gray-600 cursor-not-allowed"}
                `,disabled:f===null,children:l===w.length-1?_("finishQuiz"):_("nextQuestion")})]}))]})]})};function kp(){const[e,t]=y.useState(null),[n,r]=y.useState(null),a={};ud.forEach(d=>{a[d]=!1});const[l,o]=y.useState({includeNationalTeams:!1,excludedTeams:a}),[i,u]=y.useState(!1),[s,m]=y.useState(null);y.useEffect(()=>{const d=localStorage.getItem("gameSettings");d&&o(JSON.parse(d))},[]);const[f,h]=y.useState(null),v=()=>{try{h(null),t(Iu(l))}catch(d){h(d instanceof Error?d.message:"An error occurred"),console.error(d)}},C=d=>{const c=E=>{const P=[...E];for(let T=P.length-1;T>0;T--){const R=Math.floor(Math.random()*(T+1));[P[T],P[R]]=[P[R],P[T]]}return P},g=()=>{const E=sd(),P=c(E);return{player1Club:P[0],player2Club:P[1]}},S=["🏴󠁧󠁢󠁥󠁮󠁧󠁿 Premier League","🇪🇸 La Liga","🇮🇹 Serie A","🇩🇪 Bundesliga","🇫🇷 Ligue 1"],w={sameLeague:0,topLeagues:0,total:d,leagueCounts:{},sameLeagueCounts:{}},k={sameLeague:0,topLeagues:0,total:d,leagueCounts:{},sameLeagueCounts:{}};for(let E=0;E<d;E++){const P=Iu(l),{player1Club:T,player2Club:R}=P;T.league&&(w.leagueCounts[T.league]=(w.leagueCounts[T.league]||0)+1),R.league&&(w.leagueCounts[R.league]=(w.leagueCounts[R.league]||0)+1),T.league&&R.league&&T.league===R.league&&(w.sameLeague++,w.sameLeagueCounts[T.league]=(w.sameLeagueCounts[T.league]||0)+1),T.league&&R.league&&S.includes(T.league)&&S.includes(R.league)&&w.topLeagues++;const q=g(),{player1Club:Z,player2Club:de}=q;Z.league&&(k.leagueCounts[Z.league]=(k.leagueCounts[Z.league]||0)+1),de.league&&(k.leagueCounts[de.league]=(k.leagueCounts[de.league]||0)+1),Z.league&&de.league&&Z.league===de.league&&(k.sameLeague++,k.sameLeagueCounts[Z.league]=(k.sameLeagueCounts[Z.league]||0)+1),Z.league&&de.league&&S.includes(Z.league)&&S.includes(de.league)&&k.topLeagues++}return console.log("=== COMPARISON: WEIGHTED vs RANDOM ==="),console.log(`Same league (weighted): ${w.sameLeague}/${d} (${(w.sameLeague/d*100).toFixed(2)}%)`),console.log(`Same league (random): ${k.sameLeague}/${d} (${(k.sameLeague/d*100).toFixed(2)}%)`),console.log(`Top leagues (weighted): ${w.topLeagues}/${d} (${(w.topLeagues/d*100).toFixed(2)}%)`),console.log(`Top leagues (random): ${k.topLeagues}/${d} (${(k.topLeagues/d*100).toFixed(2)}%)`),console.log("=== DETAILED STATS ==="),console.log("Weighted - Same league counts:",w.sameLeagueCounts),console.log("Random - Same league counts:",k.sameLeagueCounts),m({weightedSameLeague:w.sameLeague,randomSameLeague:k.sameLeague,weightedTopLeagues:w.topLeagues,randomTopLeagues:k.topLeagues,total:d}),{weightedResults:w,randomResults:k}},N=d=>{o(d),localStorage.setItem("gameSettings",JSON.stringify(d))},L=n?{backgroundImage:`linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(${n.url})`,backgroundSize:"cover",backgroundPosition:"center"}:{};return p.jsx(ip,{children:p.jsxs(Dg,{children:[p.jsx(Co,{path:"/",element:p.jsxs("div",{className:"min-h-screen bg-gray-100 flex flex-col items-center justify-center p-4 relative transition-all duration-500",style:L,children:[p.jsx(Eh,{onSelect:r}),p.jsx("div",{className:"absolute top-4 right-4 z-10",children:p.jsx("button",{className:"gradient-yellow p-2 rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300",onClick:()=>u(!0),children:p.jsx(mh,{size:24})})}),p.jsxs("div",{className:"text-center mb-8",children:[p.jsx("h1",{className:`text-3xl md:text-4xl font-bold mb-2 gradient-red-blue inline-block text-transparent bg-clip-text ${n?"text-3xl md:text-4xl font-bold mb-2 gradient-red-blue inline-block text-transparent bg-clip-text glowy-white-shadow":""}`,children:"Bobboo vs Kyrillos Challenge!"}),p.jsx("p",{className:n?"text-white":"text-gray-600",children:"Generate random 4 club matchups for PES 21 Random."})]}),p.jsxs("div",{className:"flex gap-2 flex-wrap justify-center",children:[p.jsx(_u,{onClick:v,icon:p.jsx(wh,{size:17}),children:"Start"}),p.jsx(_u,{onClick:()=>C(1e3),variant:"secondary",title:"Run 1000 simulations to test the weighted selection algorithm",children:"Test Weights"})]}),f&&p.jsx("div",{className:"mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded",children:f}),e&&!f&&p.jsx(kh,{gameData:e}),s&&p.jsxs("div",{className:"mt-6 p-4 bg-white bg-opacity-90 rounded-lg shadow-lg max-w-2xl",children:[p.jsx("h2",{className:"text-xl font-bold mb-3",children:"Test Results (Weighted vs Random)"}),p.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[p.jsxs("div",{children:[p.jsx("h3",{className:"font-semibold",children:"Same League Matches:"}),p.jsxs("p",{children:["Weighted: ",s.weightedSameLeague,"/",s.total," (",(s.weightedSameLeague/s.total*100).toFixed(2),"%)"]}),p.jsxs("p",{children:["Random: ",s.randomSameLeague,"/",s.total," (",(s.randomSameLeague/s.total*100).toFixed(2),"%)"]}),p.jsxs("p",{className:"font-medium mt-1",children:["Improvement: ",((s.weightedSameLeague-s.randomSameLeague)/s.total*100).toFixed(2),"%"]})]}),p.jsxs("div",{children:[p.jsx("h3",{className:"font-semibold",children:"Top Leagues Matches:"}),p.jsxs("p",{children:["Weighted: ",s.weightedTopLeagues,"/",s.total," (",(s.weightedTopLeagues/s.total*100).toFixed(2),"%)"]}),p.jsxs("p",{children:["Random: ",s.randomTopLeagues,"/",s.total," (",(s.randomTopLeagues/s.total*100).toFixed(2),"%)"]}),p.jsxs("p",{className:"font-medium mt-1",children:["Improvement: ",((s.weightedTopLeagues-s.randomTopLeagues)/s.total*100).toFixed(2),"%"]})]})]})]}),p.jsx("div",{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:p.jsx(fr,{to:"/quiz",children:p.jsxs("button",{className:"py-2 px-5 text-lg font-bold rounded-full shadow-md hover:shadow-lg transform hover:scale-105 transition-all duration-300 focus:outline-none flex items-center justify-center alternate-gradient-1",style:{minWidth:"140px",cursor:"pointer"},title:"Test your football knowledge",children:[p.jsx("span",{className:"mr-2",children:"🧠"}),"Football Quiz"]})})}),p.jsx(Th,{isOpen:i,onClose:()=>u(!1),settings:l,onSettingsChange:N})]})}),p.jsx(Co,{path:"/quiz",element:p.jsx(xp,{})})]})})}ad(document.getElementById("root")).render(p.jsx(kp,{}));

// TypeScript interfaces for quiz data
export interface AnswerOption {
  answerText: string;
  isCorrect: boolean;
}

export interface QuizQuestion {
  questionText: string;
  answerOptions: AnswerOption[];
}

export interface ChatMessage {
  role: string;
  parts: Array<{ text: string }>;
}

export interface Language {
  code: string;
  name: string;
  flag: string;
}

export interface QuizState {
  currentQuestion: number;
  score: number;
  showScore: boolean;
  selectedAnswer: boolean | null;
  feedbackMessage: string;
  answersDisabled: boolean;
  explanation: string;
  loadingExplanation: boolean;
  selectedCategory: string | null;
  quizQuestions: QuizQuestion[];
  loadingQuestions: boolean;
  questionsError: string;
  customCategoryInput: string;
  selectedMode: string | null;
}

export interface QuizTranslations {
  home: string;
  testKnowledge: string;
  chooseMode: string;
  singlePlayer: string;
  multiPlayer: string;
  comingSoon: string;
  back: string;
  backToSelectMode: string;
  chooseCategory: string;
  customCategory: string;
  customCategoryPlaceholder: string;
  startCustomQuiz: string;
  loadingQuestions: string;
  loadingQuestionsDesc: string;
  quizCompleted: string;
  youScored: string;
  outOf: string;
  playAgain: string;
  backToHome: string;
  question: string;
  cancelQuiz: string;
  correct: string;
  incorrect: string;
  explainAnswer: string;
  loadingExplanation: string;
  explanation: string;
  nextQuestion: string;
  finishQuiz: string;
  switchToArabic: string;
  switchToEnglish: string;
  disableBackground: string;
  enableBackground: string;
}
